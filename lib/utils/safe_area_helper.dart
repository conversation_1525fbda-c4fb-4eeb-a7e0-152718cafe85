import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';

/// 安全区域助手工具
/// 专门处理刘海屏、动态岛等屏幕遮挡问题
class SafeAreaHelper {
  /// 获取安全区域信息
  static EdgeInsets getSafeAreaInsets(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// 获取屏幕尺寸信息
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// 检查是否有刘海屏或动态岛
  static bool hasNotch(BuildContext context) {
    final padding = MediaQuery.of(context).padding;
    return padding.top > 20; // iOS状态栏通常是20pt，超过则可能有刘海
  }

  /// 获取顶部安全距离
  static double getTopSafeArea(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 获取底部安全距离
  static double getBottomSafeArea(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// 为照片选择界面提供安全的布局参数
  static EdgeInsets getPhotoSelectionSafeInsets(BuildContext context) {
    final padding = MediaQuery.of(context).padding;
    
    // 确保顶部有足够的空间避开刘海屏/动态岛
    final topPadding = padding.top + 8; // 额外8pt缓冲
    
    // 确保底部有足够的空间避开Home指示器
    final bottomPadding = padding.bottom + 16; // 额外16pt缓冲
    
    return EdgeInsets.only(
      top: topPadding,
      bottom: bottomPadding,
      left: padding.left + 8,
      right: padding.right + 8,
    );
  }

  /// 为对话框提供安全的布局参数
  static EdgeInsets getDialogSafeInsets(BuildContext context) {
    final padding = MediaQuery.of(context).padding;
    
    return EdgeInsets.only(
      top: padding.top + 20,
      bottom: padding.bottom + 20,
      left: padding.left + 16,
      right: padding.right + 16,
    );
  }

  /// 创建安全的容器Widget
  static Widget createSafeContainer({
    required BuildContext context,
    required Widget child,
    Color? backgroundColor,
    bool avoidNotch = true,
    bool avoidHomeIndicator = true,
    EdgeInsets? additionalPadding,
  }) {
    final safeInsets = MediaQuery.of(context).padding;
    
    EdgeInsets finalPadding = EdgeInsets.only(
      top: avoidNotch ? safeInsets.top : 0,
      bottom: avoidHomeIndicator ? safeInsets.bottom : 0,
      left: safeInsets.left,
      right: safeInsets.right,
    );
    
    if (additionalPadding != null) {
      finalPadding = finalPadding + additionalPadding;
    }
    
    return Container(
      color: backgroundColor,
      padding: finalPadding,
      child: child,
    );
  }

  /// 创建适配刘海屏的AppBar
  static PreferredSizeWidget createSafeAppBar({
    required BuildContext context,
    required String title,
    List<Widget>? actions,
    Widget? leading,
    Color? backgroundColor,
    Color? foregroundColor,
    bool automaticallyImplyLeading = true,
  }) {
    return AppBar(
      title: Text(title),
      actions: actions,
      leading: leading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      automaticallyImplyLeading: automaticallyImplyLeading,
      // 确保AppBar内容不被刘海遮挡
      toolbarHeight: kToolbarHeight,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: 
          (backgroundColor?.computeLuminance() ?? 0) > 0.5 
            ? Brightness.dark 
            : Brightness.light,
      ),
    );
  }

  /// 为底部按钮提供安全的布局
  static Widget createSafeBottomButton({
    required BuildContext context,
    required Widget button,
    EdgeInsets? margin,
  }) {
    final bottomSafeArea = MediaQuery.of(context).padding.bottom;
    
    return Container(
      margin: margin ?? const EdgeInsets.all(16),
      padding: EdgeInsets.only(bottom: bottomSafeArea),
      child: button,
    );
  }

  /// 检查设备类型和屏幕特征
  static Map<String, dynamic> getDeviceInfo(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    
    // 检测可能的设备类型
    String deviceType = 'unknown';
    bool hasNotch = false;
    bool hasDynamicIsland = false;
    
    if (Platform.isIOS) {
      // 🔧 优化：更精确的灵动岛和刘海屏检测
      // iPhone 14 Pro/Pro Max 和 iPhone 15 Pro/Pro Max 动态岛检测
      if (padding.top >= 59.0) {
        hasDynamicIsland = true;
        deviceType = 'iPhone with Dynamic Island';
        
        // 进一步区分 Pro 和 Pro Max
        if (size.height >= 932 && size.width >= 430) {
          deviceType = 'iPhone 15 Pro Max with Dynamic Island';
        } else if (size.height >= 926 && size.width >= 428) {
          deviceType = 'iPhone 14 Pro Max with Dynamic Island';
        } else if (size.height >= 852 && size.width >= 393) {
          deviceType = 'iPhone 15 Pro with Dynamic Island';
        } else if (size.height >= 844 && size.width >= 390) {
          deviceType = 'iPhone 14 Pro with Dynamic Island';
        }
      }
      // iPhone X系列刘海屏检测 (X/XS/XR/11/12/13系列)
      else if (padding.top >= 44.0 && padding.top < 59.0) {
        hasNotch = true;
        deviceType = 'iPhone with Notch';
        
        // 根据屏幕尺寸进一步判断具体机型
        if (size.height >= 926) {
          deviceType = 'iPhone 12/13 Pro Max with Notch';
        } else if (size.height >= 896) {
          deviceType = 'iPhone 11/XR/XS Max with Notch';
        } else if (size.height >= 844) {
          deviceType = 'iPhone 12/13 Pro with Notch';
        } else if (size.height >= 812) {
          deviceType = 'iPhone X/XS/11 Pro with Notch';
        }
      }
      // 传统iPhone (iPhone 8及以下，iPhone SE系列)
      else if (padding.top >= 20.0) {
        deviceType = 'Traditional iPhone';
        
        if (size.height >= 736) {
          deviceType = 'iPhone 6/7/8 Plus';
        } else if (size.height >= 667) {
          deviceType = 'iPhone 6/7/8';
        } else if (size.height >= 568) {
          deviceType = 'iPhone SE (1st/2nd/3rd gen)';
        } else {
          deviceType = 'iPhone 5/5s/5c';
        }
      }
    }
    
    return {
      'deviceType': deviceType,
      'screenSize': size,
      'safeAreaInsets': padding,
      'devicePixelRatio': devicePixelRatio,
      'hasNotch': hasNotch,
      'hasDynamicIsland': hasDynamicIsland,
      'topSafeArea': padding.top,
      'bottomSafeArea': padding.bottom,
      'isLandscape': size.width > size.height,
    };
  }

  /// 为照片选择提供优化的布局建议
  static Map<String, dynamic> getPhotoSelectionLayoutAdvice(BuildContext context) {
    final deviceInfo = getDeviceInfo(context);
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    
    // 计算可用的安全区域
    final availableHeight = size.height - padding.top - padding.bottom;
    final availableWidth = size.width - padding.left - padding.right;
    
    // 建议的布局参数
    final suggestions = <String, dynamic>{
      'availableHeight': availableHeight,
      'availableWidth': availableWidth,
      'recommendedTopMargin': padding.top + 16,
      'recommendedBottomMargin': padding.bottom + 16,
      'recommendedSideMargin': padding.left + 16,
      'maxDialogHeight': availableHeight * 0.8,
      'maxDialogWidth': availableWidth * 0.9,
    };
    
    // 🔧 优化：根据设备类型提供精确的布局建议
    if (deviceInfo['hasDynamicIsland'] == true) {
      suggestions['specialNote'] = '检测到灵动岛，已优化顶部布局';
      suggestions['recommendedTopMargin'] = padding.top + 32; // 灵动岛需要更多空间
      suggestions['recommendedAppBarHeight'] = kToolbarHeight + 20;
      suggestions['safeAreaTopPadding'] = 12.0; // 额外的安全距离
      suggestions['pickerTopPadding'] = 16.0; // 照片选择器专用的顶部距离
    } else if (deviceInfo['hasNotch'] == true) {
      suggestions['specialNote'] = '检测到刘海屏，已优化顶部布局';
      suggestions['recommendedTopMargin'] = padding.top + 24; // 刘海屏需要适中空间
      suggestions['recommendedAppBarHeight'] = kToolbarHeight + 12;
      suggestions['safeAreaTopPadding'] = 8.0;
      suggestions['pickerTopPadding'] = 12.0;
    } else {
      suggestions['specialNote'] = '传统屏幕设备，使用标准布局';
      suggestions['recommendedTopMargin'] = padding.top + 16; // 标准设备的标准边距
      suggestions['recommendedAppBarHeight'] = kToolbarHeight;
      suggestions['safeAreaTopPadding'] = 4.0;
      suggestions['pickerTopPadding'] = 8.0;
    }

    // 🔧 新增：针对照片选择的专用配置
    suggestions['photoPickerConfig'] = {
      'safeAreaInsets': EdgeInsets.only(
        top: suggestions['pickerTopPadding'] as double,
        bottom: padding.bottom + 16,
        left: padding.left + 8,
        right: padding.right + 8,
      ),
      'gridPadding': const EdgeInsets.all(4.0),
      'itemSpacing': 2.0,
      'crossAxisCount': 4,
    };
    
    return {
      'deviceInfo': deviceInfo,
      'layoutSuggestions': suggestions,
    };
  }

  /// 创建调试信息Widget
  static Widget createDebugInfoWidget(BuildContext context) {
    final info = getPhotoSelectionLayoutAdvice(context);
    final deviceInfo = info['deviceInfo'] as Map<String, dynamic>;
    final suggestions = info['layoutSuggestions'] as Map<String, dynamic>;
    
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            '设备布局信息',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '设备类型: ${deviceInfo['deviceType']}',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Text(
            '屏幕尺寸: ${deviceInfo['screenSize'].width.toInt()}x${deviceInfo['screenSize'].height.toInt()}',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Text(
            '顶部安全区: ${deviceInfo['topSafeArea']}pt',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Text(
            '底部安全区: ${deviceInfo['bottomSafeArea']}pt',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Text(
            '可用高度: ${suggestions['availableHeight'].toInt()}pt',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          if (suggestions['specialNote'] != null) ...[
            const SizedBox(height: 4),
            Text(
              '⚠️ ${suggestions['specialNote']}',
              style: const TextStyle(color: Colors.orange, fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }
}

/// 安全区域感知的Scaffold
class SafeAreaScaffold extends StatelessWidget {
  final String title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final Color? backgroundColor;
  final Color? appBarBackgroundColor;
  final Color? appBarForegroundColor;
  final bool avoidNotch;
  final bool avoidHomeIndicator;

  const SafeAreaScaffold({
    super.key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.backgroundColor,
    this.appBarBackgroundColor,
    this.appBarForegroundColor,
    this.avoidNotch = true,
    this.avoidHomeIndicator = true,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: SafeAreaHelper.createSafeAppBar(
        context: context,
        title: title,
        actions: actions,
        backgroundColor: appBarBackgroundColor,
        foregroundColor: appBarForegroundColor,
      ),
      body: SafeAreaHelper.createSafeContainer(
        context: context,
        child: body,
        avoidNotch: avoidNotch,
        avoidHomeIndicator: avoidHomeIndicator,
      ),
      floatingActionButton: floatingActionButton,
    );
  }
}
