import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// iOS模拟器检测和处理工具
class IOSSimulatorDetector {
  static bool? _isSimulator;
  static String? _simulatorModel;
  static Map<String, dynamic>? _deviceInfo;

  /// 检测是否为iOS模拟器
  static Future<bool> isSimulator() async {
    if (_isSimulator != null) return _isSimulator!;

    if (!Platform.isIOS) {
      _isSimulator = false;
      return false;
    }

    try {
      final deviceInfo = DeviceInfoPlugin();
      final iosInfo = await deviceInfo.iosInfo;

      // 缓存设备信息
      _deviceInfo = {
        'name': iosInfo.name,
        'model': iosInfo.model,
        'systemName': iosInfo.systemName,
        'systemVersion': iosInfo.systemVersion,
        'identifierForVendor': iosInfo.identifierForVendor,
        'isPhysicalDevice': iosInfo.isPhysicalDevice,
      };

      // 检测模拟器的多种方法
      bool isSimulatorByPhysicalDevice = !iosInfo.isPhysicalDevice;
      bool isSimulatorByModel = iosInfo.model.toLowerCase().contains(
        'simulator',
      );
      bool isSimulatorByName = iosInfo.name.toLowerCase().contains('simulator');

      _isSimulator =
          isSimulatorByPhysicalDevice ||
          isSimulatorByModel ||
          isSimulatorByName;
      _simulatorModel = iosInfo.model;

      print('🔍 [iOS模拟器检测] 设备信息:');
      print('   - 设备名称: ${iosInfo.name}');
      print('   - 设备型号: ${iosInfo.model}');
      print('   - 系统版本: ${iosInfo.systemName} ${iosInfo.systemVersion}');
      print('   - 是否物理设备: ${iosInfo.isPhysicalDevice}');
      print('   - 检测结果: ${_isSimulator! ? "模拟器" : "真机"}');

      return _isSimulator!;
    } catch (e) {
      print('❌ [iOS模拟器检测] 检测失败: $e');
      // 如果检测失败，在debug模式下假设是模拟器
      _isSimulator = kDebugMode;
      return _isSimulator!;
    }
  }

  /// 获取模拟器型号
  static String? getSimulatorModel() {
    return _simulatorModel;
  }

  /// 获取设备信息
  static Map<String, dynamic>? getDeviceInfo() {
    return _deviceInfo;
  }

  /// 检查模拟器是否有照片
  static Future<bool> hasPhotosInSimulator() async {
    if (!await isSimulator()) return true; // 真机默认有照片

    // 这里可以添加更多检测逻辑
    // 目前返回true，让用户尝试选择
    return true;
  }

  /// 获取模拟器特定的错误提示
  static String getSimulatorPhotoErrorMessage() {
    return '检测到您正在使用iOS模拟器。\n\n'
        '如果无法选择照片，请按以下步骤操作：\n'
        '1. 打开模拟器的"照片"应用\n'
        '2. 从Mac拖拽照片到模拟器中\n'
        '3. 或使用 设备 > 照片 菜单添加照片\n'
        '4. 然后重新尝试选择照片';
  }

  /// 获取模拟器添加照片的详细指导
  static List<String> getSimulatorPhotoInstructions() {
    return [
      '方法一：拖拽添加',
      '• 从Mac的访达中选择照片文件',
      '• 直接拖拽到iOS模拟器窗口中',
      '• 照片会自动添加到相册',
      '',
      '方法二：菜单添加',
      '• 在模拟器菜单栏选择"设备"',
      '• 点击"照片" > "添加照片"',
      '• 选择要添加的照片文件',
      '',
      '方法三：Safari下载',
      '• 在模拟器中打开Safari',
      '• 搜索并下载图片',
      '• 长按图片选择"存储到照片"',
    ];
  }

  /// 检查是否需要显示模拟器特殊提示
  static Future<bool> shouldShowSimulatorTips() async {
    return await isSimulator();
  }

  /// 获取模拟器优化的权限请求策略
  static Future<Map<String, dynamic>> getSimulatorPermissionStrategy() async {
    if (!await isSimulator()) {
      return {'useStandardFlow': true};
    }

    return {
      'useStandardFlow': false,
      'skipPermissionCheck': false, // 仍然检查权限，但处理方式不同
      'showSimulatorTips': true,
      'allowDirectPicker': true, // 允许直接调用picker
      'customErrorHandling': true,
    };
  }

  /// 模拟器专用的错误处理
  static String handleSimulatorError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('cancelled') || errorString.contains('cancel')) {
      return '图片选择被取消。\n\n'
          '💡 模拟器提示：如果相册为空，请先添加照片：\n'
          '• 从Mac拖拽照片到模拟器\n'
          '• 或使用 设备 > 照片 菜单添加';
    }

    if (errorString.contains('permission') || errorString.contains('denied')) {
      return '相册权限被拒绝。\n\n'
          '请在设置中开启权限：\n'
          '设置 > OneDay > 照片 > 选择"所有照片"';
    }

    if (errorString.contains('no images') || errorString.contains('empty')) {
      return '模拟器相册为空。\n\n'
          '请按以下步骤添加照片：\n'
          '1. 从Mac拖拽照片到模拟器\n'
          '2. 或使用模拟器菜单：设备 > 照片 > 添加照片\n'
          '3. 然后重新尝试选择';
    }

    return '模拟器照片选择失败。\n\n'
        '常见解决方案：\n'
        '• 确保模拟器相册中有照片\n'
        '• 检查应用权限设置\n'
        '• 重启模拟器后重试\n\n'
        '错误详情：${error.toString()}';
  }

  /// 获取模拟器调试信息
  static Future<Map<String, dynamic>> getDebugInfo() async {
    final isSimulatorDevice = await isSimulator();

    return {
      'isSimulator': isSimulatorDevice,
      'simulatorModel': _simulatorModel,
      'deviceInfo': _deviceInfo,
      'platform': Platform.operatingSystem,
      'platformVersion': Platform.operatingSystemVersion,
      'isDebugMode': kDebugMode,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 重置检测缓存（用于测试）
  static void resetCache() {
    _isSimulator = null;
    _simulatorModel = null;
    _deviceInfo = null;
  }
}
