import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:oneday/features/main/main_container_page.dart';
import '../features/splash/splash_page.dart';
import '../features/auth/login_page.dart';
import '../features/auth/register_page.dart';
import '../features/auth/forgot_password_page.dart';
import '../features/auth/reset_password_page.dart';
import '../features/auth/phone_login_page.dart';
import '../features/home/<USER>';
import '../features/calendar/calendar_page.dart';

import '../features/reflection/reflection_log_page.dart';
import '../features/community/community_feed_page.dart';
import '../features/settings/settings_page.dart';
import '../features/exercise/exercise_library_page.dart';
import '../features/exercise/exercise_session_page.dart';
import '../features/exercise/focused_training_page.dart';
import '../features/time_box/timebox_list_page.dart';
import '../features/memory_palace/palace_manager_page.dart';
import '../features/onboarding/notion_style_onboarding_page.dart';
import '../features/profile/profile_page.dart';
import '../features/profile/pages/profile_edit_page.dart';
import '../features/vocabulary/vocabulary_page.dart';
import '../features/vocabulary/vocabulary_manager_page.dart';
import '../features/vocabulary/graduate_vocabulary_manager_page.dart';
import '../features/vocabulary/custom_vocabulary_manager_page.dart';
import '../features/learning_report/learning_report_page.dart';
import '../features/study_time/test_data_sync_page.dart';
import '../features/wage_system/wage_wallet_page.dart';
import '../features/wage_system/store_page.dart';
import '../features/community/community_post_editor_page.dart';
import '../debug/route_debug_page.dart';
import '../debug/image_picker_debug_page.dart';
import '../debug/simple_image_picker_test.dart';
import '../debug/ios_simulator_test_page.dart';
import '../debug/simple_photo_test.dart';
import '../features/exercise/custom_library_editor_page.dart';
import '../features/achievement/pages/achievement_page.dart';
import '../features/achievement/pages/leaderboard_page.dart';
import '../features/ability_radar/pages/ability_radar_page.dart';
import '../debug/radar_test_page.dart';

import '../features/help_feedback/help_feedback_page.dart';

/// 应用路由配置
class AppRouter {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String phoneLogin = '/phone-login';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  static const String home = '/home';
  static const String calendar = 'calendar';
  static const String store = 'store';
  static const String reflection = '/reflection';
  static const String community = 'community';
  static const String settings = '/settings';
  static const String exercise = '/exercise';
  static const String exerciseSession = '/exercise-session';
  static const String focusedTraining = '/focused-training';
  static const String timebox = '/timebox';
  static const String memoryPalace = '/memory-palace';
  static const String reflectionLog = '/reflection-log';
  static const String profile = 'profile';
  static const String profileEdit = '/profile-edit';
  static const String vocabulary = '/vocabulary';
  static const String vocabularyManager = '/vocabulary-manager';
  static const String graduateVocabularyManager =
      '/graduate-vocabulary-manager';
  static const String customVocabularyManager = '/custom-vocabulary-manager';
  static const String wageWallet = '/wage-wallet';
  static const String inventory = '/inventory';
  static const String communityPostEditor = '/community-post-editor';
  static const String routeDebug = '/route-debug';
  static const String imagePickerDebug = '/image-picker-debug';
  static const String simpleImagePickerTest = '/simple-image-picker-test';
  static const String iosSimulatorTest = '/ios-simulator-test';
  static const String simplePhotoTest = '/simple-photo-test';
  static const String customLibraryEditor = '/custom-library-editor';
  static const String sceneDetail = '/scene-detail';
  static const String photoAlbumCreator = '/photo-album-creator';
  static const String vocabularyCategory = '/vocabulary-category';
  static const String createVocabulary = '/create-vocabulary';
  static const String learningReport = '/learning-report';
  static const String testDataSync = '/test-data-sync';
  static const String vocabularyDetail = '/vocabulary-detail';
  static const String kinestheticLearning = '/kinesthetic-learning';
  static const String photoPreview = '/photo-preview';
  static const String achievement = '/achievement';
  static const String leaderboard = '/leaderboard';
  static const String abilityRadar = '/ability-radar';
  static const String radarTest = '/radar-test';

  static const String helpFeedback = '/help-feedback';

  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  // 暴露rootNavigatorKey供外部使用
  static GlobalKey<NavigatorState> get rootNavigatorKey => _rootNavigatorKey;

  static final GoRouter router = GoRouter(
    initialLocation: splash,
    navigatorKey: _rootNavigatorKey,
    routes: [
      // 启动页
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // 引导页
      GoRoute(
        path: onboarding,
        name: 'onboarding',
        builder: (context, state) => const NotionStyleOnboardingPage(),
      ),

      // 登录页
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),

      // 注册页
      GoRoute(
        path: register,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),

      // 手机号登录页
      GoRoute(
        path: phoneLogin,
        name: 'phone-login',
        builder: (context, state) => const PhoneLoginPage(),
      ),

      // 忘记密码页
      GoRoute(
        path: forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),

      // 重置密码页
      GoRoute(
        path: resetPassword,
        name: 'reset-password',
        builder: (context, state) {
          final token = state.uri.queryParameters['token'];
          return ResetPasswordPage(token: token);
        },
      ),

      // 主页面重定向
      GoRoute(path: '/main', redirect: (context, state) => '/home'),

      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return MainContainerPage(child: child);
        },
        routes: [
          GoRoute(
            path: home,
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: HomePage()),
          ),
          GoRoute(
            path: '/calendar',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: CalendarPage()),
          ),
          GoRoute(
            path: '/store',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: StorePage()),
          ),
          GoRoute(
            path: '/community',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: CommunityFeedPage()),
          ),
          GoRoute(
            path: '/profile',
            pageBuilder: (context, state) =>
                const NoTransitionPage(child: ProfilePage()),
          ),
        ],
      ),

      // 个人资料编辑页面
      GoRoute(
        path: profileEdit,
        name: 'profile-edit',
        builder: (context, state) => const ProfileEditPage(),
      ),

      // 词汇表页面
      GoRoute(
        path: vocabulary,
        name: 'vocabulary',
        builder: (context, state) => const VocabularyPage(),
      ),

      // 词汇管理页面
      GoRoute(
        path: vocabularyManager,
        name: 'vocabulary-manager',
        builder: (context, state) => const VocabularyManagerPage(),
      ),

      // 考研词库管理页面
      GoRoute(
        path: graduateVocabularyManager,
        name: 'graduate-vocabulary-manager',
        builder: (context, state) => const GraduateVocabularyManagerPage(),
      ),

      // 自定义词汇管理页面
      GoRoute(
        path: customVocabularyManager,
        name: 'custom-vocabulary-manager',
        builder: (context, state) => const CustomVocabularyManagerPage(),
      ),

      // 反思日志页面
      GoRoute(
        path: reflection,
        name: 'reflection',
        builder: (context, state) => const ReflectionLogPage(),
      ),

      // 学习报告页面
      GoRoute(
        path: learningReport,
        name: 'learning-report',
        builder: (context, state) => const LearningReportPage(),
      ),

      // 数据同步测试页面（仅开发模式）
      GoRoute(
        path: testDataSync,
        name: 'test-data-sync',
        builder: (context, state) => const TestDataSyncPage(),
      ),

      // 设置页面
      GoRoute(
        path: settings,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
      ),

      // 具身记忆页面
      GoRoute(
        path: exercise,
        name: 'exercise',
        builder: (context, state) => const ExerciseLibraryPage(),
      ),

      // 集中训练页面
      GoRoute(
        path: focusedTraining,
        name: 'focused-training',
        builder: (context, state) => const FocusedTrainingPage(),
      ),

      // 学习会话页面
      GoRoute(
        path: exerciseSession,
        name: 'exercise-session',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>? ?? {};

          return ExerciseSessionPage(
            exercises: extra['exercises'] ?? [],
            paoWord: extra['paoWord'] ?? '',
            wordPhonetic: extra['wordPhonetic'] ?? '',
            wordMeaning: extra['wordMeaning'] ?? '',
            mode: extra['mode'] ?? ExerciseMode.single,
            duration: extra['duration'] ?? 30,
          );
        },
      ),

      // 时间盒子页面
      GoRoute(
        path: timebox,
        name: 'timebox',
        builder: (context, state) => const TimeBoxListPage(),
      ),

      // 知忆相册页面
      GoRoute(
        path: memoryPalace,
        name: 'memory-palace',
        builder: (context, state) => const PalaceManagerPage(),
      ),

      // 工资钱包页面
      GoRoute(
        path: wageWallet,
        name: 'wage-wallet',
        builder: (context, state) => const WageWalletPage(),
      ),

      // 背包页面
      GoRoute(
        path: inventory,
        name: 'inventory',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>? ?? {};
          return InventoryPage(
            inventory: extra['inventory'] ?? {},
            allItems: extra['allItems'] ?? [],
          );
        },
      ),

      // 社区编辑文章页面
      GoRoute(
        path: communityPostEditor,
        name: 'community-post-editor',
        builder: (context, state) => const CommunityPostEditorPage(),
      ),

      // 路由调试页面
      GoRoute(
        path: routeDebug,
        name: 'route-debug',
        builder: (context, state) => const RouteDebugPage(),
      ),

      // 图片选择调试页面
      GoRoute(
        path: imagePickerDebug,
        name: 'image-picker-debug',
        builder: (context, state) => const ImagePickerDebugPage(),
      ),

      // 简化图片选择测试页面
      GoRoute(
        path: simpleImagePickerTest,
        name: 'simple-image-picker-test',
        builder: (context, state) => const SimpleImagePickerTest(),
      ),

      // iOS模拟器测试页面
      GoRoute(
        path: iosSimulatorTest,
        name: 'ios-simulator-test',
        builder: (context, state) => const IOSSimulatorTestPage(),
      ),

      // 简单照片测试页面
      GoRoute(
        path: simplePhotoTest,
        name: 'simple-photo-test',
        builder: (context, state) => const SimplePhotoTest(),
      ),

      // 动觉学习页面
      GoRoute(
        path: kinestheticLearning,
        name: 'kinesthetic-learning',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>? ?? {};
          return KinestheticLearningPage(
            onComplete: extra['onComplete'],
            onSkipRest: extra['onSkipRest'],
          );
        },
      ),

      // 自定义动作库编辑器页面
      GoRoute(
        path: customLibraryEditor,
        name: 'custom-library-editor',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>? ?? {};
          return CustomLibraryEditorPage(
            library: extra['library'],
            customLibraryService: extra['customLibraryService'],
          );
        },
      ),

      // 成就系统页面
      GoRoute(
        path: achievement,
        name: 'achievement',
        builder: (context, state) => const AchievementPage(),
      ),

      // 排行榜页面
      GoRoute(
        path: leaderboard,
        name: 'leaderboard',
        builder: (context, state) => const LeaderboardPage(),
      ),

      // 能力雷达图页面
      GoRoute(
        path: abilityRadar,
        name: 'ability-radar',
        builder: (context, state) => const AbilityRadarPage(),
      ),

      // 帮助与反馈页面
      GoRoute(
        path: helpFeedback,
        name: 'help-feedback',
        builder: (context, state) => const HelpFeedbackPage(),
      ),

      // 雷达图测试页面
      GoRoute(
        path: radarTest,
        name: 'radar-test',
        builder: (context, state) => const RadarTestPage(),
      ),
    ],
    errorBuilder: (context, state) => const ErrorPage(),
  );
}

/// 错误页面
class ErrorPage extends StatelessWidget {
  const ErrorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // OneDay Logo (简化版)
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF2E7EED),
                ),
                child: const Center(
                  child: Text(
                    'D',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 32),

              // 标题
              const Text(
                '页面未找到',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),

              const SizedBox(height: 16),

              // 副标题
              const Text(
                '请检查路由配置',
                style: TextStyle(fontSize: 16, color: Color(0xFF787774)),
              ),

              const SizedBox(height: 48),

              // 返回按钮
              ElevatedButton(
                onPressed: () {
                  if (context.canPop()) {
                    context.pop();
                  } else {
                    context.go('/');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2E7EED),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('返回首页'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
