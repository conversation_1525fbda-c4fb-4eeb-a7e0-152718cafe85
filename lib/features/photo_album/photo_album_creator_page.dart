import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../shared/utils/image_compression_utils.dart';
import '../../utils/ios_simulator_detector.dart';
import '../../utils/image_rendering_fix.dart';

/// 照片相册数据模型
class PhotoAlbum {
  final String id;
  final String title;
  final List<String> imagePaths;
  final DateTime createdAt;
  final DateTime lastModified;
  final String? description;
  final String? category; // 新增分类字段

  PhotoAlbum({
    required this.id,
    required this.title,
    required this.imagePaths,
    required this.createdAt,
    required this.lastModified,
    this.description,
    this.category, // 新增分类参数
  });

  /// 获取封面图片路径（第一张图片）
  String get coverImagePath => imagePaths.isNotEmpty ? imagePaths.first : '';

  /// 获取图片数量
  int get imageCount => imagePaths.length;

  /// 复制并更新图片列表
  PhotoAlbum copyWith({
    String? title,
    List<String>? imagePaths,
    DateTime? lastModified,
    String? description,
    String? category,
  }) {
    return PhotoAlbum(
      id: id,
      title: title ?? this.title,
      imagePaths: imagePaths ?? this.imagePaths,
      createdAt: createdAt,
      lastModified: lastModified ?? DateTime.now(),
      description: description ?? this.description,
      category: category ?? this.category,
    );
  }
}

/// 照片相册创建页面
class PhotoAlbumCreatorPage extends StatefulWidget {
  const PhotoAlbumCreatorPage({super.key});

  @override
  State<PhotoAlbumCreatorPage> createState() => _PhotoAlbumCreatorPageState();
}

class _PhotoAlbumCreatorPageState extends State<PhotoAlbumCreatorPage>
    with TickerProviderStateMixin {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  final List<String> _selectedImagePaths = [];
  bool _isLoading = false;
  String? _errorMessage;

  // 动画控制器
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fadeAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _slideAnimationController,
            curve: Curves.easeOutCubic,
          ),
        );
  }

  /// 直接选择图片（移除拍照选项）
  Future<void> _showImagePickerOptions() async {
    // 先检查权限，然后调用图片选择
    final hasPermission = await _checkPhotoPermission();
    if (hasPermission) {
      await _pickImages();
    }
  }

  /// 检查相册权限
  Future<bool> _checkPhotoPermission() async {
    try {
      // 检查是否为iOS模拟器，使用不同的权限策略
      final isSimulator = await IOSSimulatorDetector.isSimulator();
      final strategy =
          await IOSSimulatorDetector.getSimulatorPermissionStrategy();

      print('📸 设备类型: ${isSimulator ? "iOS模拟器" : "真机/Android"}');

      if (isSimulator && strategy['allowDirectPicker'] == true) {
        print('📸 iOS模拟器检测到，使用优化的权限流程');
        // 对于模拟器，先尝试直接检查权限，但不强制要求
        try {
          final status = await Permission.photos.status;
          print('📸 模拟器photos权限状态: $status');

          if (status.isGranted) {
            return true;
          } else if (status.isDenied) {
            final result = await Permission.photos.request();
            print('📸 模拟器权限请求结果: $result');
            // 即使权限被拒绝，也允许尝试使用image_picker
            return true;
          }
        } catch (e) {
          print('📸 模拟器权限检查异常，继续使用image_picker: $e');
        }
        return true; // 模拟器总是允许尝试
      }

      // 标准权限检查流程（真机和Android）
      Permission permission = Permission.photos;

      var status = await permission.status;
      print('📸 photos权限状态: $status');

      // 如果photos权限被拒绝且是Android，尝试storage权限
      if (Platform.isAndroid && !status.isGranted) {
        permission = Permission.storage;
        status = await permission.status;
        print('📸 storage权限状态: $status');
      }

      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        print('📸 请求相册权限...');
        final result = await permission.request();
        print('📸 权限请求结果: $result');

        if (result.isGranted) {
          return true;
        } else if (result.isPermanentlyDenied) {
          _showPermissionDeniedDialog();
          return false;
        } else {
          _showSnackBar('需要相册权限才能选择图片', isError: true);
          return false;
        }
      } else if (status.isPermanentlyDenied) {
        _showPermissionDeniedDialog();
        return false;
      }

      return false;
    } catch (e) {
      print('❌ 权限检查失败: $e');
      // 如果权限检查失败，尝试直接使用image_picker
      return true;
    }
  }

  /// 显示权限被拒绝的对话框
  void _showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要相册权限'),
        content: const Text(
          '为了选择图片，OneDay需要访问您的相册。\n\n'
          '请在设置中开启相册权限：\n'
          '设置 > OneDay > 照片',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  /// 显示iOS模拟器照片添加指导
  void _showSimulatorPhotoGuide() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.phone_iphone, color: Colors.blue),
            SizedBox(width: 8),
            Text('iOS模拟器照片指导'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                IOSSimulatorDetector.getSimulatorPhotoErrorMessage(),
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              const Text(
                '详细步骤：',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              ...IOSSimulatorDetector.getSimulatorPhotoInstructions().map(
                (instruction) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    instruction,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: instruction.startsWith('方法')
                          ? FontWeight.bold
                          : FontWeight.normal,
                      color: instruction.startsWith('•')
                          ? Colors.grey[600]
                          : null,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 重新尝试选择图片
              _showImagePickerOptions();
            },
            child: const Text('重新尝试'),
          ),
        ],
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImages() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      print('📸 开始选择图片...');

      // 检查是否是iOS模拟器并获取调试信息
      final isSimulator = await IOSSimulatorDetector.isSimulator();
      if (isSimulator) {
        print('📸 iOS模拟器检测到，使用优化流程');
        final debugInfo = await IOSSimulatorDetector.getDebugInfo();
        print('📸 模拟器调试信息: $debugInfo');

        // 检查模拟器是否有照片
        final hasPhotos = await IOSSimulatorDetector.hasPhotosInSimulator();
        print('📸 模拟器照片检查: ${hasPhotos ? "可能有照片" : "可能无照片"}');
      }

      // 先尝试单张选择，如果成功再尝试多张选择
      List<XFile> images = [];

      try {
        // 首先尝试多张选择
        images = await _imagePicker.pickMultiImage(
          imageQuality: 80, // 压缩质量，优化性能
        );
      } catch (e) {
        print('❌ 多张选择失败，尝试单张选择: $e');
        // 如果多张选择失败，尝试单张选择
        final XFile? singleImage = await _imagePicker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );
        if (singleImage != null) {
          images = [singleImage];
        }
      }

      print('📸 图片选择结果: ${images.length} 张图片');

      if (images.isNotEmpty) {
        // 🔧 新增：图片压缩处理
        final originalPaths = images.map((image) => image.path).toList();
        print('📸 选择了 ${originalPaths.length} 张图片，开始压缩处理...');

        // 显示处理中的提示
        _showSnackBar('正在处理 ${originalPaths.length} 张图片...', isError: false);

        // 批量压缩图片
        final compressedPaths = await ImageCompressionUtils.compressImages(
          imagePaths: originalPaths,
          onProgress: (current, total) {
            print('🔧 压缩进度: $current/$total');
          },
        );

        print('📸 压缩完成，成功处理 ${compressedPaths.length} 张图片');

        setState(() {
          _selectedImagePaths.addAll(compressedPaths);
        });

        // 如果是第一次添加图片，启动动画
        if (_selectedImagePaths.length == compressedPaths.length) {
          _fadeAnimationController.forward();
          _slideAnimationController.forward();
        }

        // 触觉反馈
        HapticFeedback.lightImpact();

        // 显示压缩完成提示
        if (compressedPaths.length == originalPaths.length) {
          _showSnackBar('已成功处理 ${compressedPaths.length} 张图片', isError: false);
        } else {
          _showSnackBar(
            '已处理 ${compressedPaths.length}/${originalPaths.length} 张图片',
            isError: false,
          );
        }
      } else {
        print('📸 用户取消了图片选择');

        // 使用模拟器感知的提示信息
        final isSimulator = await IOSSimulatorDetector.isSimulator();
        if (isSimulator) {
          _showSnackBar('未选择任何图片。如果相册为空，请先添加照片到模拟器', isError: false);
          // 显示模拟器添加照片的详细指导
          _showSimulatorPhotoGuide();
        } else {
          _showSnackBar('未选择任何图片', isError: false);
        }
      }
    } catch (e) {
      print('❌ 选择图片失败: $e');
      print('❌ 错误类型: ${e.runtimeType}');
      print('❌ 错误详情: ${e.toString()}');

      // 使用模拟器感知的错误处理
      final isSimulator = await IOSSimulatorDetector.isSimulator();
      String errorMessage;

      if (isSimulator) {
        // 使用模拟器专用的错误处理
        errorMessage = IOSSimulatorDetector.handleSimulatorError(e);
        print('📱 模拟器错误处理: $errorMessage');
      } else {
        // 标准错误处理
        String userGuidance = '';

        if (e.toString().contains('photo_access_denied') ||
            e.toString().contains('Permission denied')) {
          errorMessage = '相册访问权限被拒绝';
          userGuidance = '请在设置中开启相册权限';
        } else if (e.toString().contains('camera_access_denied')) {
          errorMessage = '相机访问权限被拒绝';
          userGuidance = '请在设置中开启相机权限';
        } else if (e.toString().contains('no_available_camera')) {
          errorMessage = '设备上没有可用的相机';
        } else if (Platform.isIOS && e.toString().contains('cancelled')) {
          errorMessage = '图片选择被取消';
          userGuidance = '请重试选择图片';
        } else {
          errorMessage = '选择图片失败';
          userGuidance = e.toString().length > 100 ? '请重试或联系支持' : e.toString();
        }

        if (userGuidance.isNotEmpty) {
          errorMessage = '$errorMessage：$userGuidance';
        }
      }

      setState(() {
        _errorMessage = errorMessage;
      });

      _showSnackBar(errorMessage, isError: true);

      // 如果是模拟器且错误可能与照片相关，显示详细指导
      if (isSimulator &&
          (e.toString().contains('cancelled') ||
              e.toString().contains('no images') ||
              e.toString().contains('empty'))) {
        _showSimulatorPhotoGuide();
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 清空所有图片
  Future<void> _clearAllImages() async {
    final bool? confirmed = await _showConfirmDialog(
      title: '清空全部图片',
      content: '确定要清空所有已选择的图片吗？此操作无法撤销。',
      confirmText: '清空',
      cancelText: '取消',
    );

    if (confirmed == true) {
      setState(() {
        _selectedImagePaths.clear();
      });

      // 重置动画
      _fadeAnimationController.reset();
      _slideAnimationController.reset();

      // 触觉反馈
      HapticFeedback.mediumImpact();

      _showSnackBar('已清空所有图片', isError: false);
    }
  }

  /// 移除单张图片
  void _removeImage(int index) {
    setState(() {
      _selectedImagePaths.removeAt(index);
    });

    // 如果没有图片了，重置动画
    if (_selectedImagePaths.isEmpty) {
      _fadeAnimationController.reset();
      _slideAnimationController.reset();
    }

    HapticFeedback.lightImpact();
  }

  /// 重新排序图片
  void _reorderImages(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final String item = _selectedImagePaths.removeAt(oldIndex);
      _selectedImagePaths.insert(newIndex, item);
    });

    HapticFeedback.selectionClick();
  }

  /// 显示确认对话框
  Future<bool?> _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required String cancelText,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEB5757), // 红色警告按钮
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 显示提示消息
  void _showSnackBar(String message, {bool isError = true}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? const Color(0xFFEB5757)
            : const Color(0xFF4CAF50),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// 创建相册
  Future<void> _createAlbum() async {
    final String title = _titleController.text.trim();

    if (title.isEmpty) {
      _showSnackBar('请输入相册标题');
      return;
    }

    if (_selectedImagePaths.isEmpty) {
      _showSnackBar('请至少选择一张图片');
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final album = PhotoAlbum(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        imagePaths: List.from(_selectedImagePaths),
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      // 这里可以保存到数据库或传递给父组件
      // 直接返回相册对象，不显示成功提示（相册已经在列表中显示了）
      if (mounted) {
        Navigator.of(context).pop(album);
      }
    } catch (e) {
      _showSnackBar('创建相册失败：${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('创建知忆相册'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
        actions: [
          if (_selectedImagePaths.isNotEmpty)
            TextButton(
              onPressed: _isLoading ? null : _createAlbum,
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFF2E7EED),
                        ),
                      ),
                    )
                  : const Text(
                      '创建',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2E7EED),
                      ),
                    ),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 相册信息输入区域
          _buildAlbumInfoSection(),

          const SizedBox(height: 24),

          // 图片选择按钮
          _buildImagePickerButton(),

          const SizedBox(height: 16),

          // 错误消息显示
          if (_errorMessage != null) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFEB5757).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFFEB5757).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Color(0xFFEB5757),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: const TextStyle(
                        color: Color(0xFFEB5757),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 图片预览区域
          if (_selectedImagePaths.isNotEmpty) ...[
            FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildImagePreviewSection(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建相册信息输入区域
  Widget _buildAlbumInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '相册信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),

          // 相册标题输入
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: '相册标题',
              hintText: '请输入相册标题',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFFE3E2E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFF2E7EED), width: 2),
              ),
              labelStyle: TextStyle(color: Color(0xFF6E6E6E)),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
            maxLength: 50,
            textInputAction: TextInputAction.next,
          ),

          const SizedBox(height: 16),

          // 相册描述输入
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: '相册描述（可选）',
              hintText: '请输入相册描述',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFFE3E2E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFF2E7EED), width: 2),
              ),
              labelStyle: TextStyle(color: Color(0xFF6E6E6E)),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
            maxLines: 3,
            maxLength: 200,
            textInputAction: TextInputAction.done,
          ),
        ],
      ),
    );
  }

  /// 构建图片选择按钮
  Widget _buildImagePickerButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _showImagePickerOptions,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.add_photo_alternate_outlined),
        label: Text(
          _selectedImagePaths.isEmpty ? '从相册选择图片' : '添加更多图片',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2E7EED),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),
    );
  }

  /// 构建图片预览区域
  Widget _buildImagePreviewSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和操作按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '已选择 ${_selectedImagePaths.length} 张图片',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              if (_selectedImagePaths.isNotEmpty)
                TextButton.icon(
                  onPressed: _clearAllImages,
                  icon: const Icon(
                    Icons.clear_all,
                    size: 18,
                    color: Color(0xFFEB5757),
                  ),
                  label: const Text(
                    '清空全部',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFFEB5757),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
            ],
          ),

          const SizedBox(height: 12),

          // 封面预览提示
          if (_selectedImagePaths.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Color(0xFF2E7EED),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '第一张图片将作为相册封面',
                      style: TextStyle(
                        color: Color(0xFF2E7EED),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 图片预览列表
          _buildImagePreviewList(),

          const SizedBox(height: 16),

          // 从相册添加图片按钮
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _pickImages,
              icon: const Icon(Icons.photo_library_outlined),
              label: const Text('从相册添加图片'),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF2E7EED),
                side: const BorderSide(color: Color(0xFF2E7EED)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片预览列表（支持拖拽排序和响应式设计）
  Widget _buildImagePreviewList() {
    if (_selectedImagePaths.isEmpty) {
      return const SizedBox.shrink();
    }

    // 响应式设计：根据屏幕宽度调整图片大小
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final imageSize = isTablet ? 140.0 : 100.0;
    final spacing = isTablet ? 12.0 : 8.0;

    return SizedBox(
      height: imageSize + 20, // 额外空间用于拖拽效果
      child: ReorderableListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImagePaths.length,
        onReorder: _reorderImages,
        proxyDecorator: (child, index, animation) {
          return AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              final double animValue = Curves.easeInOut.transform(
                animation.value,
              );
              final double elevation = lerpDouble(0, 6, animValue)!;
              final double scale = lerpDouble(1, 1.05, animValue)!;

              return Transform.scale(
                scale: scale,
                child: Material(
                  elevation: elevation,
                  borderRadius: BorderRadius.circular(12),
                  child: child,
                ),
              );
            },
            child: child,
          );
        },
        itemBuilder: (context, index) {
          final imagePath = _selectedImagePaths[index];
          final isFirst = index == 0;

          return Container(
            key: ValueKey(imagePath),
            width: imageSize,
            margin: EdgeInsets.only(
              right: index < _selectedImagePaths.length - 1 ? spacing : 0,
            ),
            child: Stack(
              children: [
                // 图片容器
                Container(
                  width: imageSize,
                  height: imageSize,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: isFirst
                        ? Border.all(color: const Color(0xFF2E7EED), width: 2)
                        : Border.all(color: const Color(0xFFE3E2E0), width: 1),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: GestureDetector(
                      onTap: () => _showImagePreview(index),
                      child: ImageRenderingFix.buildOptimizedImageFile(
                        File(imagePath),
                        fit: BoxFit.cover,
                        errorWidget: Container(
                          color: const Color(0xFFF5F5F5),
                          child: Icon(
                            Icons.broken_image,
                            color: const Color(0xFF9B9A97),
                            size: imageSize * 0.3,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // 封面标识
                if (isFirst)
                  Positioned(
                    top: 4,
                    left: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7EED),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        '封面',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                // 删除按钮
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      width: isTablet ? 28 : 24,
                      height: isTablet ? 28 : 24,
                      decoration: BoxDecoration(
                        color: const Color(0xFFEB5757),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: isTablet ? 18 : 16,
                      ),
                    ),
                  ),
                ),

                // 拖拽手柄
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    width: isTablet ? 28 : 24,
                    height: isTablet ? 28 : 24,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Icon(
                      Icons.drag_handle,
                      color: Colors.white,
                      size: isTablet ? 18 : 16,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 显示图片预览
  void _showImagePreview(int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _ImagePreviewPage(
          imagePaths: _selectedImagePaths,
          initialIndex: initialIndex,
        ),
      ),
    );
  }
}

/// 图片预览页面
class _ImagePreviewPage extends StatefulWidget {
  final List<String> imagePaths;
  final int initialIndex;

  const _ImagePreviewPage({
    required this.imagePaths,
    required this.initialIndex,
  });

  @override
  State<_ImagePreviewPage> createState() => _ImagePreviewPageState();
}

class _ImagePreviewPageState extends State<_ImagePreviewPage> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          '${_currentIndex + 1} / ${widget.imagePaths.length}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: widget.imagePaths.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemBuilder: (context, index) {
          return InteractiveViewer(
            minScale: 0.5,
            maxScale: 3.0,
            child: Center(
              child: ImageRenderingFix.buildOptimizedImageFile(
                File(widget.imagePaths[index]),
                fit: BoxFit.contain,
                errorWidget: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.broken_image, color: Colors.white54, size: 64),
                      SizedBox(height: 16),
                      Text(
                        '图片加载失败',
                        style: TextStyle(color: Colors.white54, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
