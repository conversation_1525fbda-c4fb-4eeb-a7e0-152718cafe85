import 'dart:io';
import 'dart:convert';
import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'scene_detail_page.dart';
import '../photo_album/photo_album_creator_page.dart';
import '../../shared/utils/image_compression_utils.dart';
import '../../utils/image_rendering_fix.dart';
import '../achievement/providers/achievement_provider.dart';
import '../../utils/safe_area_helper.dart';

/// 分类节点的数据模型（升级版）
class CategoryNode {
  final String id; // 唯一标识
  String title; // 可编辑标题
  List<CategoryNode> children; // 子节点列表
  bool isExpanded; // 展开状态
  bool isEditing; // 编辑状态
  bool isNew; // 新创建标记
  PrivacyStatus privacyStatus; // 隐私状态
  int level; // 层级（动态计算）

  CategoryNode({
    String? id,
    required this.title,
    List<CategoryNode>? children,
    this.isExpanded = false,
    this.isEditing = false,
    this.isNew = false,
    this.privacyStatus = PrivacyStatus.private,
    this.level = 0,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       children = children ?? [];

  /// 复制节点
  CategoryNode copyWith({
    String? id,
    String? title,
    List<CategoryNode>? children,
    bool? isExpanded,
    bool? isEditing,
    bool? isNew,
    PrivacyStatus? privacyStatus,
    int? level,
  }) {
    return CategoryNode(
      id: id ?? this.id,
      title: title ?? this.title,
      children: children ?? List.from(this.children),
      isExpanded: isExpanded ?? this.isExpanded,
      isEditing: isEditing ?? this.isEditing,
      isNew: isNew ?? this.isNew,
      privacyStatus: privacyStatus ?? this.privacyStatus,
      level: level ?? this.level,
    );
  }

  /// 序列化为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'children': children.map((child) => child.toJson()).toList(),
      'isExpanded': isExpanded,
      'privacyStatus': privacyStatus.index,
      'level': level,
    };
  }

  /// 从JSON反序列化
  static CategoryNode fromJson(Map<String, dynamic> json) {
    return CategoryNode(
      id: json['id'] as String,
      title: json['title'] as String,
      children: (json['children'] as List<dynamic>)
          .map((child) => CategoryNode.fromJson(child as Map<String, dynamic>))
          .toList(),
      isExpanded: json['isExpanded'] as bool? ?? false,
      privacyStatus: PrivacyStatus.values[json['privacyStatus'] as int? ?? 0],
      level: json['level'] as int? ?? 0,
    );
  }

  /// 递归更新所有子节点的层级
  void updateChildrenLevels() {
    for (var child in children) {
      child.level = level + 1;
      child.updateChildrenLevels();
    }
  }

  /// 查找节点（递归）
  CategoryNode? findNodeById(String nodeId) {
    if (id == nodeId) return this;
    for (var child in children) {
      final found = child.findNodeById(nodeId);
      if (found != null) return found;
    }
    return null;
  }

  /// 获取所有后代节点（扁平化）
  List<CategoryNode> getAllDescendants() {
    List<CategoryNode> descendants = [];
    for (var child in children) {
      descendants.add(child);
      descendants.addAll(child.getAllDescendants());
    }
    return descendants;
  }
}

/// 隐私状态枚举
enum PrivacyStatus { public, private }

/// 分类管理器
class CategoryManager {
  static const String _storageKey = 'category_tree_data';

  List<CategoryNode> _categories = [];
  List<CategoryNode> _searchResults = [];
  String _searchQuery = '';

  List<CategoryNode> get categories => _categories;
  List<CategoryNode> get searchResults => _searchResults;
  String get searchQuery => _searchQuery;
  bool get isSearching => _searchQuery.isNotEmpty;

  /// 初始化默认分类数据
  void initializeDefaultCategories() {
    _categories = [
      CategoryNode(
        id: 'school',
        title: '学校',
        level: 0,
        isExpanded: true,
        children: [
          CategoryNode(id: 'preschool', title: '学前', level: 1),
          CategoryNode(id: 'primary', title: '小学', level: 1),
          CategoryNode(id: 'middle', title: '中学', level: 1),
          CategoryNode(id: 'high', title: '高中', level: 1),
          CategoryNode(id: 'university', title: '大学', level: 1),
        ],
      ),
      CategoryNode(
        id: 'work',
        title: '工作',
        level: 0,
        children: [
          CategoryNode(id: 'company', title: '公司', level: 1),
          CategoryNode(id: 'dormitory', title: '宿舍', level: 1),
        ],
      ),
      CategoryNode(
        id: 'residence',
        title: '居所',
        level: 0,
        children: [
          CategoryNode(id: 'own_home', title: '己舍', level: 1),
          CategoryNode(id: 'friend_home', title: '友家', level: 1),
          CategoryNode(id: 'relative_home', title: '亲房', level: 1),
        ],
      ),
      CategoryNode(
        id: 'scenic',
        title: '景点',
        level: 0,
        children: [
          CategoryNode(id: 'natural', title: '自然', level: 1),
          CategoryNode(id: 'cultural', title: '人文', level: 1),
        ],
      ),
    ];
  }

  /// 添加新节点
  void addNode(String title, {CategoryNode? parent, int? insertIndex}) {
    final newNode = CategoryNode(
      title: title,
      isNew: true,
      level: parent?.level ?? 0,
    );

    if (parent != null) {
      // 添加为子节点
      newNode.level = parent.level + 1;
      if (insertIndex != null && insertIndex <= parent.children.length) {
        parent.children.insert(insertIndex, newNode);
      } else {
        parent.children.add(newNode);
      }
      parent.isExpanded = true; // 自动展开父节点
    } else {
      // 添加为根节点
      if (insertIndex != null && insertIndex <= _categories.length) {
        _categories.insert(insertIndex, newNode);
      } else {
        _categories.add(newNode);
      }
    }

    _updateSearchResults();
  }

  /// 删除节点
  bool deleteNode(String nodeId) {
    return _deleteNodeRecursive(_categories, nodeId);
  }

  bool _deleteNodeRecursive(List<CategoryNode> nodes, String nodeId) {
    for (int i = 0; i < nodes.length; i++) {
      if (nodes[i].id == nodeId) {
        nodes.removeAt(i);
        _updateSearchResults();
        return true;
      }
      if (_deleteNodeRecursive(nodes[i].children, nodeId)) {
        return true;
      }
    }
    return false;
  }

  /// 查找节点
  CategoryNode? findNodeById(String nodeId) {
    for (var category in _categories) {
      final found = category.findNodeById(nodeId);
      if (found != null) return found;
    }
    return null;
  }

  /// 获取节点的父节点
  CategoryNode? getParentNode(String nodeId) {
    return _getParentNodeRecursive(_categories, nodeId);
  }

  CategoryNode? _getParentNodeRecursive(
    List<CategoryNode> nodes,
    String nodeId,
  ) {
    for (var node in nodes) {
      // 检查直接子节点
      for (var child in node.children) {
        if (child.id == nodeId) return node;
      }
      // 递归检查子节点
      final found = _getParentNodeRecursive(node.children, nodeId);
      if (found != null) return found;
    }
    return null;
  }

  /// 搜索分类
  void searchCategories(String query) {
    _searchQuery = query.trim();
    _updateSearchResults();
  }

  void _updateSearchResults() {
    if (_searchQuery.isEmpty) {
      _searchResults = [];
      return;
    }

    _searchResults = [];
    _searchInNodes(_categories, _searchQuery.toLowerCase());
  }

  void _searchInNodes(List<CategoryNode> nodes, String query) {
    for (var node in nodes) {
      if (node.title.toLowerCase().contains(query)) {
        _searchResults.add(node);
      }
      _searchInNodes(node.children, query);
    }
  }

  /// 获取过滤后的分类列表
  List<CategoryNode> getFilteredCategories() {
    return isSearching ? _searchResults : _categories;
  }

  /// 清除搜索
  void clearSearch() {
    _searchQuery = '';
    _searchResults = [];
  }

  /// 保存到本地存储
  Future<void> saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = {
        'categories': _categories.map((cat) => cat.toJson()).toList(),
        'version': '2.0', // 更新版本号以强制更新移除emoji的分类
        'lastModified': DateTime.now().toIso8601String(),
      };
      await prefs.setString(_storageKey, json.encode(data));
      print('💾 已保存分类树数据到本地存储');
    } catch (e) {
      print('❌ 保存分类树数据失败: $e');
    }
  }

  /// 从本地存储加载
  Future<void> loadFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(_storageKey);

      if (dataString != null) {
        final data = json.decode(dataString) as Map<String, dynamic>;
        final version = data['version'] as String? ?? '1.0';

        // 检查版本号，如果是旧版本则重置为新的默认分类
        if (version != '2.0') {
          print('🔄 检测到旧版本分类数据 ($version)，更新为新版本 (2.0)');
          initializeDefaultCategories();
          await saveToStorage();
          return;
        }

        final categoriesJson = data['categories'] as List<dynamic>;
        _categories = categoriesJson
            .map((cat) => CategoryNode.fromJson(cat as Map<String, dynamic>))
            .toList();

        print('📂 已从本地存储加载分类树数据 (版本: $version)');
      } else {
        // 首次启动，使用默认数据
        initializeDefaultCategories();
        await saveToStorage();
      }
    } catch (e) {
      print('❌ 加载分类树数据失败，使用默认数据: $e');
      initializeDefaultCategories();
    }
  }

  /// 重置为默认分类
  Future<void> resetToDefault() async {
    initializeDefaultCategories();
    await saveToStorage();
  }
}

/// 记忆宫殿数据模型
class MemoryPalace {
  final String id;
  final String title;
  final List<String> imagePaths;
  final int anchorCount;
  final List<String> tags;
  final String category;
  final DateTime createdAt;
  final DateTime lastUsed;
  final bool isFromGallery;

  MemoryPalace({
    required this.id,
    required this.title,
    required this.imagePaths,
    required this.anchorCount,
    required this.tags,
    required this.category,
    required this.createdAt,
    required this.lastUsed,
    this.isFromGallery = false,
  });

  String get imagePath => imagePaths.isNotEmpty ? imagePaths.first : '';

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'imagePaths': imagePaths,
      'anchorCount': anchorCount,
      'tags': tags,
      'category': category,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUsed': lastUsed.millisecondsSinceEpoch,
      'isFromGallery': isFromGallery,
    };
  }

  /// 从JSON创建实例
  factory MemoryPalace.fromJson(Map<String, dynamic> json) {
    return MemoryPalace(
      id: json['id'] as String,
      title: json['title'] as String,
      imagePaths: List<String>.from(json['imagePaths'] as List),
      anchorCount: json['anchorCount'] as int,
      tags: List<String>.from(json['tags'] as List),
      category: json['category'] as String,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] as int),
      lastUsed: DateTime.fromMillisecondsSinceEpoch(json['lastUsed'] as int),
      isFromGallery: json['isFromGallery'] as bool? ?? false,
    );
  }
}

/// 记忆宫殿管理页
class PalaceManagerPage extends ConsumerStatefulWidget {
  const PalaceManagerPage({super.key});

  @override
  ConsumerState<PalaceManagerPage> createState() => _PalaceManagerPageState();
}

class _PalaceManagerPageState extends ConsumerState<PalaceManagerPage> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  String? _selectedCategory;

  // 存储键
  static const String _palacesStorageKey = 'oneday_memory_palaces';

  // 相册列表（从持久化存储加载）
  List<MemoryPalace> _palaces = [];
  bool _isSidebarVisible = false;
  bool _isDisposing = false; // 标记widget是否正在dispose
  bool _isEditingInProgress = false; // 标记是否有编辑正在进行
  bool _isCreatingPalace = false; // 防止重复创建相册

  // 分类管理器
  final CategoryManager _categoryManager = CategoryManager();

  List<MemoryPalace> get _filteredPalaces {
    return _palaces.where((palace) {
      final matchesSearch = palace.title.toLowerCase().contains(
        _searchQuery.toLowerCase(),
      );
      final matchesCategory =
          _selectedCategory == null || palace.category == _selectedCategory;
      return matchesSearch && matchesCategory;
    }).toList()..sort((a, b) => b.lastUsed.compareTo(a.lastUsed));
  }

  @override
  void initState() {
    super.initState();
    _loadPalacesFromStorage();
    _initializeCategoryManager();
  }

  /// 初始化分类管理器
  Future<void> _initializeCategoryManager() async {
    await _categoryManager.loadFromStorage();
    setState(() {}); // 触发UI更新
  }

  @override
  void dispose() {
    _isDisposing = true; // 标记widget正在dispose
    _searchController.dispose();
    _saveTimer?.cancel(); // 取消防抖定时器
    super.dispose();
  }

  /// 从本地存储加载相册数据
  Future<void> _loadPalacesFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_palacesStorageKey);

      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        final palaces = jsonList
            .map((json) => MemoryPalace.fromJson(json))
            .toList();

        setState(() {
          _palaces = palaces;
        });

        print('✅ 成功加载 ${palaces.length} 个知忆相册');
      } else {
        // 首次启动，加载默认示例数据
        await _loadDefaultPalaces();
        print('🆕 首次启动，加载默认示例数据');
      }
    } catch (e) {
      print('❌ 加载知忆相册数据失败: $e');
      // 加载失败时使用默认数据
      await _loadDefaultPalaces();
    }
  }

  /// 加载默认示例数据（仅首次启动时）
  Future<void> _loadDefaultPalaces() async {
    final defaultPalaces = [
      MemoryPalace(
        id: '1',
        title: '我的卧室',
        imagePaths: [
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400',
        ],
        anchorCount: 12,
        tags: ['睡眠', '休息'],
        category: '己舍',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        lastUsed: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      MemoryPalace(
        id: '2',
        title: '图书馆',
        imagePaths: [
          'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
        ],
        anchorCount: 8,
        tags: ['学习', '安静'],
        category: '大学',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        lastUsed: DateTime.now().subtract(const Duration(days: 1)),
      ),
      MemoryPalace(
        id: '3',
        title: '小学教室',
        imagePaths: [
          'https://images.unsplash.com/photo-1580582932707-520aed937b7b?w=400',
        ],
        anchorCount: 15,
        tags: ['基础', '启蒙'],
        category: '小学',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        lastUsed: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    setState(() {
      _palaces = defaultPalaces;
    });

    // 保存默认数据到本地存储
    await _savePalacesToStorage();
  }

  /// 保存相册数据到本地存储
  Future<void> _savePalacesToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _palaces.map((palace) => palace.toJson()).toList();
      final jsonString = json.encode(jsonList);

      await prefs.setString(_palacesStorageKey, jsonString);
      print('💾 已保存 ${_palaces.length} 个知忆相册到本地存储');
    } catch (e) {
      print('❌ 保存知忆相册数据失败: $e');
    }
  }

  void _toggleSidebar() {
    setState(() {
      _isSidebarVisible = !_isSidebarVisible;
    });
  }

  void _onNodeSelected(CategoryNode node) {
    setState(() {
      if (_selectedCategory == node.title) {
        _selectedCategory = null;
      } else {
        _selectedCategory = node.title;
      }
      _isSidebarVisible = false;
    });
  }

  Timer? _saveTimer; // 防抖定时器

  /// 通知编辑状态变化
  void _notifyEditingStateChanged(bool isEditing) {
    _isEditingInProgress = isEditing;
    print('📝 编辑状态变更: $isEditing');
  }

  /// 分类变更回调（增强防抖机制和安全性）
  void _onCategoriesChanged() {
    if (!mounted) {
      print('⚠️ Widget已unmounted，跳过分类变更回调');
      return;
    }

    try {
      // 如果CategorySidebar正在编辑，延迟UI更新直到编辑完成
      // 这样可以避免在编辑过程中重建widget导致unmounted错误
      if (_isEditingInProgress) {
        print('⚠️ 编辑正在进行中，延迟分类变更回调');
        return;
      }

      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!_isDisposing && mounted) {
          setState(() {
            // 触发UI更新
          });
        }
      });

      // 使用防抖机制避免频繁保存
      _saveTimer?.cancel();
      _saveTimer = Timer(const Duration(milliseconds: 500), () async {
        // 双重检查
        if (!_isDisposing && mounted && _saveTimer != null) {
          try {
            await _categoryManager.saveToStorage();
            print('✅ 分类数据保存成功');
          } catch (e) {
            print('❌ 分类数据保存失败: $e');
          }
        } else {
          print('⚠️ Widget已unmounted或Timer已取消，跳过数据保存');
        }
      });
    } catch (e, stackTrace) {
      print('❌ 分类变更回调执行失败: $e');
      print('Stack trace: $stackTrace');
    }
  }

  /// 构建带动画的悬浮按钮
  Widget _buildAnimatedFloatingActionButton() {
    return AnimatedSlide(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      offset: _isSidebarVisible ? const Offset(0, 2) : Offset.zero,
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        opacity: _isSidebarVisible ? 0.0 : 1.0,
        child: FloatingActionButton.extended(
          onPressed: _isSidebarVisible ? null : _showCreatePalaceDialog,
          backgroundColor: const Color(0xFF2E7EED),
          foregroundColor: Colors.white,
          icon: const Icon(Icons.add),
          label: const Text('创建知忆相册'),
          heroTag: "createPalaceButton", // 避免hero动画冲突
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      body: Stack(
        children: [
          Column(
            children: [
              _buildAppBar(),
              _buildSearchFilter(),
              Expanded(
                child: _filteredPalaces.isEmpty
                    ? _buildEmptyState()
                    : _buildPalaceGrid(),
              ),
            ],
          ),
          CategorySidebar(
            key: const ValueKey('category_sidebar'), // 添加key保持状态
            isVisible: _isSidebarVisible,
            onClose: _toggleSidebar,
            onNodeSelected: _onNodeSelected,
            categoryManager: _categoryManager,
            onCategoriesChanged: _onCategoriesChanged,
            onEditingStateChanged: _notifyEditingStateChanged,
          ),
        ],
      ),
      floatingActionButton: _buildAnimatedFloatingActionButton(),
    );
  }

  Widget _buildAppBar() {
    return SafeArea(
      bottom: false,
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            IconButton(
              icon: const Icon(Icons.menu, color: Color(0xFF37352F)),
              onPressed: _toggleSidebar,
              tooltip: '打开分类',
            ),
            Expanded(
              child: Text(
                _selectedCategory ?? '知忆相册',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.help_outline, color: Color(0xFF9B9A97)),
              onPressed: _showUsageDialog,
              tooltip: '使用帮助',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchFilter() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFEBEAE9),
          borderRadius: BorderRadius.circular(8),
        ),
        child: TextField(
          controller: _searchController,
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.w400,
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
          decoration: InputDecoration(
            hintText: '搜索知忆相册...',
            hintStyle: const TextStyle(color: Color(0xFF9B9A97), fontSize: 16),
            prefixIcon: const Icon(
              Icons.search,
              color: Color(0xFF787774),
              size: 20,
            ),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: Color(0xFF787774),
                      size: 20,
                    ),
                    onPressed: () {
                      _searchController.clear();
                      setState(() {
                        _searchQuery = '';
                      });
                    },
                  )
                : null,
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPalaceGrid() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 响应式布局调整
          int columns = 2;
          double aspectRatio = 0.75;

          if (constraints.maxWidth > 900) {
            columns = 4;
            aspectRatio = 0.7;
          } else if (constraints.maxWidth > 600) {
            columns = 3;
            aspectRatio = 0.72;
          }

          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columns,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: aspectRatio,
            ),
            itemCount: _filteredPalaces.length,
            itemBuilder: (context, index) {
              return MemoryPalaceCard(
                palace: _filteredPalaces[index],
                onTap: () => _openPalaceDetail(_filteredPalaces[index]),
                onLongPress: () => _showPalaceOptions(_filteredPalaces[index]),
                onMoreOptions: () =>
                    _showPalaceMoreOptions(_filteredPalaces[index]),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    if (_searchQuery.isNotEmpty || _selectedCategory != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.search_off, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            const Text(
              '没有找到相关结果',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Text('尝试调整搜索或筛选条件', style: TextStyle(color: Colors.grey[600])),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _searchQuery = '';
                  _selectedCategory = null;
                });
              },
              child: const Text('清除筛选'),
            ),
          ],
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.psychology, size: 60, color: Color(0xFF2E7EED)),
          const SizedBox(height: 24),
          const Text(
            '还没有知忆相册',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Text(
            '创建你的第一个知忆相册\n让学习变得更有趣',
            style: TextStyle(color: Colors.grey[600], height: 1.5),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showUsageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: const [
            Icon(Icons.help_outline, color: Color(0xFF2F76DA), size: 24),
            SizedBox(width: 8),
            Text('使用说明'),
          ],
        ),
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '知忆相册使用指南：',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              const SizedBox(height: 16),

              // 1. 创建相册
              _buildUsageItem(
                icon: Icons.photo_library,
                title: '创建相册',
                description: '点击"创建知忆相册"按钮，选择图片并为相册命名',
              ),

              // 2. 记忆宫殿
              _buildUsageItem(
                icon: Icons.account_balance,
                title: '记忆宫殿',
                description: '利用熟悉的场景图片建立记忆关联，提高学习效率',
              ),

              // 3. 标记知识点
              _buildUsageItem(
                icon: Icons.place,
                title: '标记知识点',
                description: '在图片上标记重要位置，关联知识点和记忆内容',
              ),

              // 4. 分类管理
              _buildUsageItem(
                icon: Icons.folder,
                title: '分类管理',
                description: '使用标签和分类对相册进行分类整理',
              ),

              // 5. 复习训练
              _buildUsageItem(
                icon: Icons.refresh,
                title: '复习训练',
                description: '定期访问相册进行记忆训练和知识复习',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF2F76DA),
            ),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: const Color(0xFF2F76DA), size: 18),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: const Color(0xFF37352F).withValues(alpha: 0.8),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _openPalaceDetail(MemoryPalace palace) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SceneDetailPage(
          sceneId: palace.id,
          sceneTitle: palace.title,
          sceneImagePath: palace.imagePaths.isNotEmpty
              ? palace.imagePaths[0]
              : '',
          palaceImagePaths: palace.imagePaths,
          onAnchorCountChanged: (sceneId, anchorCount) {
            _updatePalaceAnchorCount(sceneId, anchorCount);
          },
        ),
      ),
    );
  }

  /// 更新相册的知识点数量
  void _updatePalaceAnchorCount(String palaceId, int anchorCount) {
    final index = _palaces.indexWhere((palace) => palace.id == palaceId);
    if (index != -1) {
      final palace = _palaces[index];
      final updatedPalace = MemoryPalace(
        id: palace.id,
        title: palace.title,
        imagePaths: palace.imagePaths,
        anchorCount: anchorCount, // 更新知识点数量
        tags: palace.tags,
        category: palace.category,
        createdAt: palace.createdAt,
        lastUsed: DateTime.now(), // 更新最后使用时间
        isFromGallery: palace.isFromGallery,
      );

      setState(() {
        _palaces[index] = updatedPalace;
      });

      // 保存到本地存储
      _savePalacesToStorage();

      print('📊 更新相册知识点统计: ${palace.title} -> $anchorCount 个知识点');
    }
  }

  void _showPalaceOptions(MemoryPalace palace) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                palace.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 12),
            ListTile(
              leading: const Icon(Icons.edit_outlined),
              title: const Text('编辑'),
              onTap: () {
                Navigator.pop(context);
                // _showEditPalaceDialog(palace); // Placeholder
              },
            ),
            ListTile(
              leading: const Icon(Icons.copy_outlined),
              title: const Text('复制图片'),
              subtitle: const Text('复制图片创建新地点'),
              onTap: () {
                Navigator.pop(context);
                // _copyPalaceImages(palace); // Placeholder
              },
            ),
            ListTile(
              leading: const Icon(Icons.download_outlined),
              title: const Text('导出知识点'),
              onTap: () {
                Navigator.pop(context);
                // _exportKnowledgePoints(palace); // Placeholder
              },
            ),
            ListTile(
              leading: Icon(Icons.delete_outline, color: Colors.red.shade400),
              title: Text('删除', style: TextStyle(color: Colors.red.shade400)),
              onTap: () {
                Navigator.pop(context);
                _deletePalace(palace);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 显示三点菜单选项（新的弹出菜单）
  void _showPalaceMoreOptions(MemoryPalace palace) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      elevation: 8,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        side: BorderSide(color: Color(0xFFE3E2E0), width: 1),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部拖拽指示器
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: const Color(0xFFE3E2E0),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 20),
            // 相册标题
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                palace.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF37352F),
                ),
              ),
            ),
            const SizedBox(height: 16),
            // 编辑选项
            ListTile(
              leading: const Icon(
                Icons.edit_outlined,
                color: Color(0xFF2F76DA),
              ),
              title: const Text(
                '编辑',
                style: TextStyle(fontSize: 16, color: Color(0xFF37352F)),
              ),
              subtitle: const Text(
                '编辑相册标题和描述',
                style: TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
              ),
              onTap: () {
                Navigator.pop(context);
                _showEditPalaceDialog(palace);
              },
            ),
            // 复制图片选项
            ListTile(
              leading: const Icon(
                Icons.content_copy_outlined,
                color: Color(0xFF2F76DA),
              ),
              title: const Text(
                '复制图片',
                style: TextStyle(fontSize: 16, color: Color(0xFF37352F)),
              ),
              subtitle: const Text(
                '复制所有图片创建新相册',
                style: TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
              ),
              onTap: () {
                Navigator.pop(context);
                _copyPalaceImages(palace);
              },
            ),
            // 更改分类选项
            ListTile(
              leading: const Icon(
                Icons.folder_outlined,
                color: Color(0xFF2F76DA),
              ),
              title: const Text(
                '更改分类',
                style: TextStyle(fontSize: 16, color: Color(0xFF37352F)),
              ),
              subtitle: const Text(
                '移动到其他分类文件夹',
                style: TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
              ),
              onTap: () {
                Navigator.pop(context);
                _changePalaceCategory(palace);
              },
            ),
            // 导出知识点选项
            ListTile(
              leading: const Icon(
                Icons.share_outlined,
                color: Color(0xFF2F76DA),
              ),
              title: const Text(
                '导出知识点',
                style: TextStyle(fontSize: 16, color: Color(0xFF37352F)),
              ),
              subtitle: const Text(
                '导出图片和标注内容',
                style: TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
              ),
              onTap: () {
                Navigator.pop(context);
                _exportKnowledgePoints(palace);
              },
            ),
            // 删除选项
            ListTile(
              leading: Icon(Icons.delete_outline, color: Colors.red.shade400),
              title: Text(
                '删除',
                style: TextStyle(fontSize: 16, color: Colors.red.shade400),
              ),
              subtitle: Text(
                '永久删除此相册',
                style: TextStyle(fontSize: 14, color: Colors.red.shade300),
              ),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmDialog(palace);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 显示编辑相册对话框
  Future<void> _showEditPalaceDialog(MemoryPalace palace) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _EditAlbumDialog(
        palace: palace,
        categoryData: _categoryManager.categories,
        onAlbumUpdated: (updatedAlbum) {
          _updatePalace(updatedAlbum);
        },
      ),
    );
  }

  /// 更新相册信息
  Future<void> _updatePalace(PhotoAlbum updatedAlbum) async {
    final index = _palaces.indexWhere((p) => p.id == updatedAlbum.id);
    if (index != -1) {
      final updatedPalace = MemoryPalace(
        id: updatedAlbum.id,
        title: updatedAlbum.title,
        imagePaths: updatedAlbum.imagePaths,
        anchorCount: _palaces[index].anchorCount, // 保持原有的锚点数量
        tags: _palaces[index].tags, // 保持原有的标签
        category: updatedAlbum.category ?? _palaces[index].category,
        createdAt: _palaces[index].createdAt, // 保持原有的创建时间
        lastUsed: DateTime.now(), // 更新最后使用时间
        isFromGallery: _palaces[index].isFromGallery,
      );

      setState(() {
        _palaces[index] = updatedPalace;
      });

      // 保存到本地存储
      await _savePalacesToStorage();

      _showSnackBar('成功更新相册"${updatedAlbum.title}"');
      print('✅ 成功更新知忆相册: ${updatedAlbum.title} (ID: ${updatedAlbum.id})');
    }
  }

  /// 复制相册图片
  Future<void> _copyPalaceImages(MemoryPalace palace) async {
    try {
      // 显示确认对话框
      final bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('复制图片'),
          content: Text(
            '确定要复制相册"${palace.title}"中的所有图片吗？\n这将创建一个新的相册，不包含知识标注。',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2F76DA),
                foregroundColor: Colors.white,
              ),
              child: const Text('复制'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // 创建新的相册副本
      final newPalace = MemoryPalace(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: '${palace.title} - 副本',
        imagePaths: List.from(palace.imagePaths), // 复制图片路径列表
        anchorCount: 0, // 新相册没有知识点
        tags: List.from(palace.tags), // 复制标签
        category: palace.category, // 保持相同分类
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
        isFromGallery: true, // 标记为用户创建的相册
      );

      setState(() {
        _palaces.insert(0, newPalace); // 插入到列表开头
      });

      // 保存到本地存储
      await _savePalacesToStorage();

      _showSnackBar('成功复制相册"${palace.title}"', isError: false);
      print(
        '✅ 成功复制知忆相册: ${palace.title} -> ${newPalace.title} (ID: ${newPalace.id})',
      );
    } catch (e) {
      _showSnackBar('复制相册失败：${e.toString()}');
      print('❌ 复制知忆相册失败: $e');
    }
  }

  /// 更改相册分类
  Future<void> _changePalaceCategory(MemoryPalace palace) async {
    final String? newCategory = await showDialog<String>(
      context: context,
      builder: (context) => _CategorySelectionDialog(
        categoryData: _categoryManager.categories,
        currentCategory: palace.category,
        palaceTitle: palace.title,
      ),
    );

    if (newCategory != null && newCategory != palace.category) {
      try {
        // 创建更新后的相册
        final updatedPalace = MemoryPalace(
          id: palace.id,
          title: palace.title,
          imagePaths: palace.imagePaths,
          anchorCount: palace.anchorCount,
          tags: palace.tags,
          category: newCategory,
          createdAt: palace.createdAt,
          lastUsed: DateTime.now(), // 更新最后使用时间
          isFromGallery: palace.isFromGallery,
        );

        // 更新列表中的相册
        final index = _palaces.indexWhere((p) => p.id == palace.id);
        if (index != -1) {
          setState(() {
            _palaces[index] = updatedPalace;
          });

          // 保存到本地存储
          await _savePalacesToStorage();

          _showSnackBar(
            '成功将相册"${palace.title}"移动到"$newCategory"',
            isError: false,
          );
          print('✅ 成功更改相册分类: ${palace.title} -> $newCategory');
        }
      } catch (e) {
        _showSnackBar('更改分类失败：${e.toString()}');
        print('❌ 更改相册分类失败: $e');
      }
    }
  }

  /// 导出知识点
  Future<void> _exportKnowledgePoints(MemoryPalace palace) async {
    try {
      // 显示导出选项对话框
      final String? exportType = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('导出知识点'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('选择导出格式：'),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.image_outlined),
                title: const Text('图片集合'),
                subtitle: const Text('导出所有图片到相册'),
                onTap: () => Navigator.of(context).pop('images'),
              ),
              ListTile(
                leading: const Icon(Icons.share_outlined),
                title: const Text('分享内容'),
                subtitle: const Text('分享相册信息和图片'),
                onTap: () => Navigator.of(context).pop('share'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        ),
      );

      if (exportType == null) return;

      switch (exportType) {
        case 'images':
          await _exportAsImages(palace);
          break;
        case 'share':
          await _shareKnowledgePoints(palace);
          break;
      }
    } catch (e) {
      _showSnackBar('导出失败：${e.toString()}');
      print('❌ 导出知识点失败: $e');
    }
  }

  /// 导出为图片集合（无权限方案）
  Future<void> _exportAsImages(MemoryPalace palace) async {
    try {
      _showSnackBar('正在准备图片分享...', isError: false);

      // 显示导出选项对话框
      final String? exportOption = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('选择导出方式'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.share, color: Color(0xFF2F76DA)),
                title: const Text('系统分享'),
                subtitle: const Text('通过系统分享功能导出图片'),
                onTap: () => Navigator.of(context).pop('share'),
              ),
              ListTile(
                leading: const Icon(Icons.copy, color: Color(0xFF2F76DA)),
                title: const Text('复制图片路径'),
                subtitle: const Text('复制图片文件路径到剪贴板'),
                onTap: () => Navigator.of(context).pop('copy_paths'),
              ),
              ListTile(
                leading: const Icon(
                  Icons.info_outline,
                  color: Color(0xFF2F76DA),
                ),
                title: const Text('查看图片信息'),
                subtitle: const Text('显示图片详细信息'),
                onTap: () => Navigator.of(context).pop('info'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        ),
      );

      if (exportOption == null) return;

      switch (exportOption) {
        case 'share':
          await _shareImagesViaSystem(palace);
          break;
        case 'copy_paths':
          await _copyImagePaths(palace);
          break;
        case 'info':
          await _showImageInfo(palace);
          break;
      }
    } catch (e) {
      _showSnackBar('导出失败：${e.toString()}');
    }
  }

  /// 通过系统分享功能分享图片（无需权限）
  Future<void> _shareImagesViaSystem(MemoryPalace palace) async {
    try {
      // 构建分享内容
      final StringBuffer shareContent = StringBuffer();
      shareContent.writeln('📚 ${palace.title}');
      shareContent.writeln('🖼️ 包含 ${palace.imagePaths.length} 张图片');
      shareContent.writeln('📁 分类：${palace.category}');
      shareContent.writeln('');
      shareContent.writeln('图片列表：');

      for (int i = 0; i < palace.imagePaths.length; i++) {
        final imagePath = palace.imagePaths[i];
        final fileName = imagePath.split('/').last;
        shareContent.writeln('${i + 1}. $fileName');
      }

      shareContent.writeln('\n📱 来自OneDay知忆相册');

      // 显示分享内容对话框
      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('图片信息'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                SelectableText(
                  shareContent.toString(),
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF0F8FF),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFF2F76DA).withValues(alpha: 0.3),
                    ),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '💡 无权限导出说明',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2F76DA),
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '• 系统分享：通过iOS/Android原生分享功能\n'
                        '• 无需权限：不需要相册访问权限\n'
                        '• 用户选择：用户可选择保存到相册或其他应用',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6E6E6E),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
            ElevatedButton(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: shareContent.toString()));
                Navigator.of(context).pop();
                _showSnackBar('内容已复制到剪贴板', isError: false);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2F76DA),
                foregroundColor: Colors.white,
              ),
              child: const Text('复制'),
            ),
          ],
        ),
      );

      _showSnackBar('图片信息已准备完成', isError: false);
    } catch (e) {
      _showSnackBar('分享失败：${e.toString()}');
    }
  }

  /// 复制图片路径到剪贴板
  Future<void> _copyImagePaths(MemoryPalace palace) async {
    try {
      final paths = palace.imagePaths.join('\n');
      await Clipboard.setData(ClipboardData(text: paths));
      _showSnackBar('图片路径已复制到剪贴板', isError: false);
    } catch (e) {
      _showSnackBar('复制失败：${e.toString()}');
    }
  }

  /// 显示图片详细信息
  Future<void> _showImageInfo(MemoryPalace palace) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${palace.title} - 图片信息'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: palace.imagePaths.length,
            itemBuilder: (context, index) {
              final imagePath = palace.imagePaths[index];
              final fileName = imagePath.split('/').last;
              final isNetworkImage = imagePath.startsWith('http');

              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: const Color(0xFF2F76DA),
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ),
                  title: Text(
                    fileName,
                    style: const TextStyle(fontSize: 14),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isNetworkImage ? '网络图片' : '本地图片',
                        style: TextStyle(
                          fontSize: 12,
                          color: isNetworkImage ? Colors.blue : Colors.green,
                        ),
                      ),
                      Text(
                        imagePath,
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.grey,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  trailing: IconButton(
                    icon: const Icon(Icons.copy, size: 16),
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: imagePath));
                      _showSnackBar('路径已复制', isError: false);
                    },
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 分享知识点内容
  Future<void> _shareKnowledgePoints(MemoryPalace palace) async {
    try {
      // 构建分享内容
      final StringBuffer content = StringBuffer();
      content.writeln('📚 知忆相册：${palace.title}');
      content.writeln('🏷️ 分类：${palace.category}');
      content.writeln('📊 知识点数量：${palace.anchorCount}个');
      content.writeln('🖼️ 图片数量：${palace.imagePaths.length}张');
      content.writeln('📅 创建时间：${_formatDate(palace.createdAt)}');

      if (palace.tags.isNotEmpty) {
        content.writeln('🏷️ 标签：${palace.tags.join(', ')}');
      }

      content.writeln('\n📱 来自OneDay知忆相册应用');

      // 这里可以使用share_plus插件来分享内容
      // 暂时显示内容并复制到剪贴板
      await _showShareDialog(content.toString());
    } catch (e) {
      _showSnackBar('分享失败：${e.toString()}');
    }
  }

  /// 显示分享对话框
  Future<void> _showShareDialog(String content) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('分享内容'),
        content: SingleChildScrollView(
          child: SelectableText(content, style: const TextStyle(fontSize: 14)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              // 复制到剪贴板
              Clipboard.setData(ClipboardData(text: content));
              Navigator.of(context).pop();
              _showSnackBar('内容已复制到剪贴板', isError: false);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2F76DA),
              foregroundColor: Colors.white,
            ),
            child: const Text('复制'),
          ),
        ],
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 显示删除确认对话框
  Future<void> _showDeleteConfirmDialog(MemoryPalace palace) async {
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除相册"${palace.title}"吗？\n此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade400,
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deletePalace(palace);
    }
  }

  Future<void> _deletePalace(MemoryPalace palace) async {
    setState(() {
      _palaces.removeWhere((p) => p.id == palace.id);
    });

    // 保存到本地存储
    await _savePalacesToStorage();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已删除 "${palace.title}"'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(milliseconds: 500), // 设置为0.5秒显示时间
        ),
      );
    }

    print('🗑️ 已删除知忆相册: ${palace.title} (ID: ${palace.id})');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red.shade400 : Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _showCreatePalaceDialog() async {
    // 防止重复创建
    if (_isCreatingPalace) {
      print('⚠️ 相册创建正在进行中，忽略重复请求');
      return;
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _CreateAlbumDialog(
        categoryData: _categoryManager.categories,
        currentCategory: _selectedCategory, // 传递当前选中的分类
        onAlbumCreated: (album) async {
          await _createPalace(
            album.title,
            album.category ?? album.description ?? '默认分类', // 优先使用category字段
            [],
            album.imagePaths,
          );
        },
      ),
    );
  }

  Future<void> _createPalace(
    String title,
    String category,
    List<String> tags,
    List<String> imagePaths,
  ) async {
    // 设置创建标志，防止重复创建
    if (_isCreatingPalace) {
      print('⚠️ 相册创建正在进行中，忽略重复请求');
      return;
    }

    setState(() {
      _isCreatingPalace = true;
    });

    try {
      final newPalace = MemoryPalace(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        imagePaths: imagePaths,
        anchorCount: 0,
        tags: tags,
        category: category,
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
        isFromGallery: true, // 标记为用户创建的相册
      );

      setState(() {
        _palaces.insert(0, newPalace);
      });

      // 保存到本地存储
      await _savePalacesToStorage();

      print('✅ 成功创建并保存知忆相册: $title (ID: ${newPalace.id})');

      // 触发创建记忆宫殿成就（在界面更新后）
      await ref.read(achievementProvider.notifier).onMemoryPalaceCreated();

      // 不显示SnackBar，避免与成就通知冲突
      // _showSnackBar('成功创建记忆宫殿"$title"');
    } catch (e) {
      print('❌ 创建知忆相册失败: $e');
      _showSnackBar('创建相册失败：${e.toString()}');
    } finally {
      // 重置创建标志
      if (mounted) {
        setState(() {
          _isCreatingPalace = false;
        });
      }
    }
  }
}

/// 创建知忆相册对话框
class _CreateAlbumDialog extends StatefulWidget {
  final List<CategoryNode> categoryData;
  final Function(PhotoAlbum) onAlbumCreated;
  final String? currentCategory; // 新增当前分类参数

  const _CreateAlbumDialog({
    required this.categoryData,
    required this.onAlbumCreated,
    this.currentCategory, // 新增当前分类参数
  });

  @override
  State<_CreateAlbumDialog> createState() => _CreateAlbumDialogState();
}

class _CreateAlbumDialogState extends State<_CreateAlbumDialog>
    with TickerProviderStateMixin {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  final List<String> _selectedImagePaths = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _selectedCategory; // 选中的分类

  // 动画控制器
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    // 初始化分类选择：如果有当前分类则使用，否则为null（需要用户选择）
    _selectedCategory = widget.currentCategory;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fadeAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _slideAnimationController,
            curve: Curves.easeOutCubic,
          ),
        );
  }

  /// 直接选择图片（移除拍照选项）
  Future<void> _showImagePickerOptions() async {
    // 直接调用图片选择，不再显示选项菜单
    await _pickImages();
  }

  /// 选择图片
  Future<void> _pickImages() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 先尝试单张选择，如果成功再尝试多张选择
      List<XFile> images = [];

      try {
        // 首先尝试多张选择
        images = await _imagePicker.pickMultiImage(
          imageQuality: 80, // 压缩质量，优化性能
        );
      } catch (e) {
        print('❌ 多张选择失败，尝试单张选择: $e');
        // 如果多张选择失败，尝试单张选择
        final XFile? singleImage = await _imagePicker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );
        if (singleImage != null) {
          images = [singleImage];
        }
      }

      if (images.isNotEmpty) {
        // 🔧 新增：图片压缩处理
        final originalPaths = images.map((image) => image.path).toList();
        print('📸 选择了 ${originalPaths.length} 张图片，开始压缩处理...');

        // 批量压缩图片
        final compressedPaths = await ImageCompressionUtils.compressImages(
          imagePaths: originalPaths,
          onProgress: (current, total) {
            print('🔧 压缩进度: $current/$total');
          },
        );

        setState(() {
          _selectedImagePaths.addAll(compressedPaths);
        });

        // 如果是第一次添加图片，启动动画
        if (_selectedImagePaths.length == compressedPaths.length) {
          _fadeAnimationController.forward();
          _slideAnimationController.forward();
        }

        // 触觉反馈
        HapticFeedback.lightImpact();

        // 显示压缩完成提示
        if (compressedPaths.length == originalPaths.length) {
          _showSnackBar('已成功处理 ${compressedPaths.length} 张图片', isError: false);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = '选择图片失败：${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 清空所有图片
  Future<void> _clearAllImages() async {
    final bool? confirmed = await _showConfirmDialog(
      title: '清空全部图片',
      content: '确定要清空所有已选择的图片吗？此操作无法撤销。',
      confirmText: '清空',
      cancelText: '取消',
    );

    if (confirmed == true) {
      setState(() {
        _selectedImagePaths.clear();
      });

      // 重置动画
      _fadeAnimationController.reset();
      _slideAnimationController.reset();

      // 触觉反馈
      HapticFeedback.mediumImpact();

      _showSnackBar('已清空所有图片', isError: false);
    }
  }

  /// 移除单张图片
  void _removeImage(int index) {
    setState(() {
      _selectedImagePaths.removeAt(index);
    });

    // 如果没有图片了，重置动画
    if (_selectedImagePaths.isEmpty) {
      _fadeAnimationController.reset();
      _slideAnimationController.reset();
    }

    HapticFeedback.lightImpact();
  }

  /// 重新排序图片
  void _reorderImages(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final String item = _selectedImagePaths.removeAt(oldIndex);
      _selectedImagePaths.insert(newIndex, item);
    });

    HapticFeedback.selectionClick();
  }

  /// 显示确认对话框
  Future<bool?> _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required String cancelText,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEB5757), // 红色警告按钮
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 显示提示消息
  void _showSnackBar(
    String message, {
    bool isError = true,
    Duration? duration,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? const Color(0xFFEB5757)
            : const Color(0xFF4CAF50),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: duration ?? const Duration(seconds: 4), // 默认4秒，可自定义
      ),
    );
  }

  /// 创建相册
  Future<void> _createAlbum() async {
    final String title = _titleController.text.trim();

    if (title.isEmpty) {
      setState(() {
        _errorMessage = '请输入相册标题';
      });
      return;
    }

    if (_selectedImagePaths.isEmpty) {
      setState(() {
        _errorMessage = '请至少选择一张图片';
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final album = PhotoAlbum(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title,
        imagePaths: List.from(_selectedImagePaths),
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        category: _selectedCategory, // 使用选中的分类信息
      );

      // 调用回调函数
      widget.onAlbumCreated(album);

      // 直接关闭对话框，不显示成功提示（相册已经在列表中显示了）
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _errorMessage = '创建相册失败：${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取安全的对话框布局参数，确保避开刘海屏和动态岛
    final safeInsets = SafeAreaHelper.getDialogSafeInsets(context);
    final screenSize = MediaQuery.of(context).size;

    // 计算弹窗的最大高度，确保在屏幕上方显示
    final maxDialogHeight =
        screenSize.height - safeInsets.top - safeInsets.bottom - 40;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.only(
        top: safeInsets.top,
        bottom: safeInsets.bottom,
        left: safeInsets.left,
        right: safeInsets.right,
      ),
      alignment: Alignment.topCenter, // 关键修改：将弹窗对齐到屏幕上方
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: maxDialogHeight.clamp(400, 700), // 动态计算最大高度
        ),
        decoration: BoxDecoration(
          color: Colors.white, // 使用与时间盒子相同的白色背景
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  const Expanded(
                    child: Text(
                      '创建知忆相册',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // 内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 1. 图片选择按钮（移到最顶部）
                    _buildImagePickerButton(),

                    const SizedBox(height: 16),

                    // 错误消息显示
                    if (_errorMessage != null) ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEB5757).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(
                              0xFFEB5757,
                            ).withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.error_outline,
                              color: Color(0xFFEB5757),
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(
                                  color: Color(0xFFEB5757),
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // 2. 图片预览区域（紧跟在选择图片按钮之后）
                    if (_selectedImagePaths.isNotEmpty) ...[
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: SlideTransition(
                          position: _slideAnimation,
                          child: _buildImagePreviewSection(),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // 3. 相册信息输入区域（移到图片预览区域下方）
                    _buildAlbumInfoSection(),
                  ],
                ),
              ),
            ),

            // 底部操作按钮
            if (_selectedImagePaths.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  border: Border(
                    top: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF6E6E6E),
                          side: const BorderSide(color: Color(0xFFE3E2E0)),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('取消'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _createAlbum,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2F76DA),
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : const Text(
                                '创建相册',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建相册信息输入区域
  Widget _buildAlbumInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '相册信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),

          // 相册标题输入
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: '相册标题',
              hintText: '请输入相册标题',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFFE3E2E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFF2F76DA), width: 2),
              ),
              labelStyle: TextStyle(color: Color(0xFF6E6E6E)),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
            maxLength: 50,
            textInputAction: TextInputAction.next,
          ),

          const SizedBox(height: 16),

          // 相册描述输入
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: '相册描述（可选）',
              hintText: '请输入相册描述',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFFE3E2E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFF2F76DA), width: 2),
              ),
              labelStyle: TextStyle(color: Color(0xFF6E6E6E)),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
            maxLines: 3,
            maxLength: 200,
            textInputAction: TextInputAction.done,
          ),

          // 分类选择（仅在没有当前分类时显示）
          if (widget.currentCategory == null) ...[
            const SizedBox(height: 16),
            _buildCategorySelector(),
          ] else ...[
            const SizedBox(height: 16),
            _buildCurrentCategoryDisplay(),
          ],
        ],
      ),
    );
  }

  /// 构建图片选择按钮
  Widget _buildImagePickerButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _showImagePickerOptions,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.add_photo_alternate_outlined),
        label: Text(
          _selectedImagePaths.isEmpty ? '从相册选择图片' : '添加更多图片',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2F76DA),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),
    );
  }

  /// 构建图片预览区域
  Widget _buildImagePreviewSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题和操作按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '已选择 ${_selectedImagePaths.length} 张图片',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              if (_selectedImagePaths.isNotEmpty)
                TextButton.icon(
                  onPressed: _clearAllImages,
                  icon: const Icon(
                    Icons.clear_all,
                    size: 18,
                    color: Color(0xFFEB5757),
                  ),
                  label: const Text(
                    '清空全部',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFFEB5757),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
            ],
          ),

          const SizedBox(height: 12),

          // 封面预览提示
          if (_selectedImagePaths.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF2F76DA).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Color(0xFF2F76DA),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '第一张图片将作为相册封面',
                      style: TextStyle(
                        color: Color(0xFF2F76DA),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 图片预览列表
          _buildImagePreviewList(),

          const SizedBox(height: 16),

          // 从相册添加图片按钮
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _showImagePickerOptions,
              icon: const Icon(Icons.photo_library_outlined),
              label: const Text('从相册添加图片'),
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF2F76DA),
                side: const BorderSide(color: Color(0xFF2F76DA)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片预览列表（支持拖拽排序和响应式设计）
  Widget _buildImagePreviewList() {
    if (_selectedImagePaths.isEmpty) {
      return const SizedBox.shrink();
    }

    // 响应式设计：根据屏幕宽度调整图片大小
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final imageSize = isTablet ? 120.0 : 90.0; // 对话框中使用较小尺寸
    final spacing = isTablet ? 12.0 : 8.0;

    return SizedBox(
      height: imageSize + 20, // 额外空间用于拖拽效果
      child: ReorderableListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImagePaths.length,
        onReorder: _reorderImages,
        proxyDecorator: (child, index, animation) {
          return AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              final double animValue = Curves.easeInOut.transform(
                animation.value,
              );
              final double elevation = lerpDouble(0, 6, animValue)!;
              final double scale = lerpDouble(1, 1.05, animValue)!;

              return Transform.scale(
                scale: scale,
                child: Material(
                  elevation: elevation,
                  borderRadius: BorderRadius.circular(12),
                  child: child,
                ),
              );
            },
            child: child,
          );
        },
        itemBuilder: (context, index) {
          final imagePath = _selectedImagePaths[index];
          final isFirst = index == 0;

          return Container(
            key: ValueKey(imagePath),
            width: imageSize,
            margin: EdgeInsets.only(
              right: index < _selectedImagePaths.length - 1 ? spacing : 0,
            ),
            child: Stack(
              children: [
                // 图片容器
                Container(
                  width: imageSize,
                  height: imageSize,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: isFirst
                        ? Border.all(color: const Color(0xFF2F76DA), width: 2)
                        : Border.all(color: const Color(0xFFE3E2E0), width: 1),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: GestureDetector(
                      onTap: () => _showImagePreview(index),
                      child: ImageRenderingFix.buildOptimizedImageFile(
                        File(imagePath),
                        fit: BoxFit.cover,
                        errorWidget: Container(
                          color: const Color(0xFFF5F5F5),
                          child: Icon(
                            Icons.broken_image,
                            color: const Color(0xFF9B9A97),
                            size: imageSize * 0.3,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // 封面标识
                if (isFirst)
                  Positioned(
                    top: 4,
                    left: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2F76DA),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        '封面',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                // 删除按钮
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: const Color(0xFFEB5757),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),

                // 拖拽手柄
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.drag_handle,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 显示图片预览
  void _showImagePreview(int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _ImagePreviewPage(
          imagePaths: _selectedImagePaths,
          initialIndex: initialIndex,
        ),
      ),
    );
  }

  /// 构建分类选择器
  Widget _buildCategorySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择分类',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: const InputDecoration(
            labelText: '分类',
            border: OutlineInputBorder(),
          ),
          dropdownColor: const Color(0xFFFFFFFF), // 纯白色背景 #FFFFFF
          hint: const Text('请选择分类', style: TextStyle(color: Color(0xFF6E6E6E))),
          items: _buildCategoryDropdownItems(),
          onChanged: (String? newValue) {
            setState(() {
              _selectedCategory = newValue;
            });
          },
        ),
      ],
    );
  }

  /// 构建当前分类显示
  Widget _buildCurrentCategoryDisplay() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '分类',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
            border: Border.all(
              color: const Color(0xFF2F76DA).withValues(alpha: 0.3),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              const Icon(Icons.folder, color: Color(0xFF2F76DA), size: 20),
              const SizedBox(width: 8),
              Text(
                widget.currentCategory ?? '默认分类',
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF2F76DA),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建分类下拉菜单项
  List<DropdownMenuItem<String>> _buildCategoryDropdownItems() {
    List<DropdownMenuItem<String>> items = [];

    // 添加默认选项
    items.add(DropdownMenuItem<String>(value: null, child: const Text('默认分类')));

    // 递归添加分类节点
    void addCategoryItems(List<CategoryNode> categories, int level) {
      for (final category in categories) {
        items.add(
          DropdownMenuItem<String>(
            value: category.title,
            child: Padding(
              padding: EdgeInsets.only(left: level * 16.0),
              child: Text(category.title),
            ),
          ),
        );

        if (category.children.isNotEmpty) {
          addCategoryItems(category.children, level + 1);
        }
      }
    }

    addCategoryItems(widget.categoryData, 0);
    return items;
  }

  // 已删除未使用的 _getCategoryColor 方法

  // 已删除未使用的 _getCategoryIcon 方法
}

/// 图片预览页面
class _ImagePreviewPage extends StatefulWidget {
  final List<String> imagePaths;
  final int initialIndex;

  const _ImagePreviewPage({
    required this.imagePaths,
    required this.initialIndex,
  });

  @override
  State<_ImagePreviewPage> createState() => _ImagePreviewPageState();
}

class _ImagePreviewPageState extends State<_ImagePreviewPage> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          '${_currentIndex + 1} / ${widget.imagePaths.length}',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: widget.imagePaths.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemBuilder: (context, index) {
          return InteractiveViewer(
            minScale: 0.5,
            maxScale: 3.0,
            child: Center(
              child: ImageRenderingFix.buildOptimizedImageFile(
                File(widget.imagePaths[index]),
                fit: BoxFit.contain,
                errorWidget: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.broken_image, color: Colors.white54, size: 64),
                      SizedBox(height: 16),
                      Text(
                        '图片加载失败',
                        style: TextStyle(color: Colors.white54, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// 分类侧边栏组件（升级版）
class CategorySidebar extends StatefulWidget {
  final bool isVisible;
  final VoidCallback onClose;
  final Function(CategoryNode) onNodeSelected;
  final CategoryManager categoryManager;
  final VoidCallback onCategoriesChanged;
  final Function(bool) onEditingStateChanged;

  const CategorySidebar({
    super.key,
    required this.isVisible,
    required this.onClose,
    required this.onNodeSelected,
    required this.categoryManager,
    required this.onCategoriesChanged,
    required this.onEditingStateChanged,
  });

  @override
  State<CategorySidebar> createState() => _CategorySidebarState();
}

class _CategorySidebarState extends State<CategorySidebar> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _isSearchExpanded = false;
  String? _editingNodeId;
  final Map<String, TextEditingController> _editControllers = {};
  final Map<String, FocusNode> _editFocusNodes = {};
  final Map<String, GlobalKey> _editTextFieldKeys = {}; // 保存TextField的GlobalKey
  final Map<String, String> _originalTitles = {}; // 保存编辑前的原始标题
  bool _isDisposing = false; // 标记widget是否正在dispose
  final Map<String, GlobalKey<PopupMenuButtonState>> _popupMenuKeys =
      {}; // PopupMenu的GlobalKey
  Timer? _saveTimer; // 防抖定时器

  // 拖拽相关状态
  String? _draggingNodeId;
  final Map<String, GlobalKey> _nodeKeys = {}; // 节点的GlobalKey

  // 分界线显示状态
  String? _insertionLineTargetId; // 分界线目标节点ID
  bool _insertionLineAbove = false; // 分界线是否在目标节点上方

  /// 获取PopupMenu的GlobalKey
  GlobalKey<PopupMenuButtonState> _getPopupMenuKey(String nodeId) {
    if (!_popupMenuKeys.containsKey(nodeId)) {
      _popupMenuKeys[nodeId] = GlobalKey<PopupMenuButtonState>();
    }
    return _popupMenuKeys[nodeId]!;
  }

  /// 获取节点的GlobalKey
  GlobalKey _getNodeKey(String nodeId) {
    if (!_nodeKeys.containsKey(nodeId)) {
      _nodeKeys[nodeId] = GlobalKey();
    }
    return _nodeKeys[nodeId]!;
  }

  /// 关闭所有打开的PopupMenu
  void _closeAllPopupMenus() {
    if (_isDisposing || !mounted) return;

    for (var entry in _popupMenuKeys.entries) {
      try {
        final key = entry.value;
        final nodeId = entry.key;

        if (key.currentState?.mounted == true && key.currentContext != null) {
          // 检查是否有PopupRoute打开
          final navigator = Navigator.of(key.currentContext!);
          if (navigator.canPop()) {
            // 只关闭PopupRoute，不影响其他路由
            navigator.popUntil((route) => route is! PopupRoute);
            print('✅ 关闭节点 $nodeId 的PopupMenu');
          }
        }
      } catch (e) {
        print('⚠️ 关闭PopupMenu时发生错误: $e');
      }
    }
  }

  @override
  void dispose() {
    // 标记widget正在dispose，防止异步操作继续执行
    _isDisposing = true;

    _searchController.dispose();
    _searchFocusNode.dispose();
    _saveTimer?.cancel(); // 清理防抖定时器

    // 关闭所有PopupMenu
    _closeAllPopupMenus();

    // 清理PopupMenu keys
    _popupMenuKeys.clear();

    // 清理节点keys
    _nodeKeys.clear();

    // 安全地清理所有编辑状态
    for (var nodeId in _editControllers.keys.toList()) {
      _cleanupEditingState(nodeId);
    }

    super.dispose();
  }

  /// 构建顶部操作栏
  Widget _buildTopBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE3E2E0), width: 1)),
      ),
      child: Column(
        children: [
          // 标题栏和操作按钮
          Row(
            children: [
              const Text(
                '分类',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              const Spacer(),
              // 搜索按钮
              IconButton(
                icon: Icon(
                  _isSearchExpanded ? Icons.search_off : Icons.search,
                  color: const Color(0xFF37352F),
                  size: 20,
                ),
                onPressed: _toggleSearch,
                tooltip: _isSearchExpanded ? '关闭搜索' : '搜索分类',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
              const SizedBox(width: 4),
              // 添加按钮
              IconButton(
                icon: const Icon(Icons.add, color: Color(0xFF37352F), size: 20),
                onPressed: _addNewCategory,
                tooltip: '添加分类',
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ),
          // 搜索框（展开时显示）
          if (_isSearchExpanded) ...[
            const SizedBox(height: 8),
            TextField(
              controller: _searchController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: '搜索分类...',
                hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                prefixIcon: const Icon(
                  Icons.search,
                  color: Color(0xFF9B9A97),
                  size: 18,
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(
                          Icons.clear,
                          color: Color(0xFF9B9A97),
                          size: 18,
                        ),
                        onPressed: _clearSearch,
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: const TextStyle(fontSize: 14),
              onChanged: _onSearchChanged,
            ),
          ],
        ],
      ),
    );
  }

  /// 切换搜索状态
  void _toggleSearch() {
    if (_isDisposing || !mounted) return;
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
      if (!_isSearchExpanded) {
        _clearSearch();
      } else {
        // 延迟聚焦，确保UI已更新
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!_isDisposing && mounted) {
            _searchFocusNode.requestFocus();
          }
        });
      }
    });
  }

  /// 清除搜索
  void _clearSearch() {
    _searchController.clear();
    widget.categoryManager.clearSearch();
    if (!_isDisposing && mounted) {
      setState(() {});
    }
  }

  /// 搜索内容变更
  void _onSearchChanged(String query) {
    widget.categoryManager.searchCategories(query);
    if (!_isDisposing && mounted) {
      setState(() {});
    }
  }

  /// 添加新分类
  void _addNewCategory() {
    if (!mounted) return;

    final newTitle = '新分类';
    widget.categoryManager.addNode(newTitle);

    // 找到新添加的节点并进入编辑模式
    final categories = widget.categoryManager.categories;
    final newNode = categories.last;
    _startEditing(newNode.id, newTitle);

    // 不在编辑过程中调用onCategoriesChanged，避免widget重建
  }

  /// 添加子分类
  void _addChildCategory(CategoryNode parentNode) {
    if (!mounted) return;

    print('🎯 为节点 ${parentNode.title} 添加子分类');

    final newTitle = '新子分类';

    // 使用CategoryManager的addNode方法，传入parent参数
    widget.categoryManager.addNode(newTitle, parent: parentNode);

    // 确保父节点展开
    if (!parentNode.isExpanded) {
      if (!_isDisposing && mounted) {
        setState(() {
          parentNode.isExpanded = true;
        });
      }
    }

    // 找到新添加的子节点并进入编辑模式
    final newChildNode = parentNode.children.last;

    // 延迟执行编辑，确保UI更新完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isDisposing && mounted) {
        _startEditing(newChildNode.id, newTitle);
        // 触发分类变更回调
        widget.onCategoriesChanged();
        print('✅ 成功添加子分类: ${newChildNode.title} (父节点: ${parentNode.title})');
      }
    });
  }

  /// 开始编辑节点（使用Dialog替代内联编辑）
  void _startEditing(String nodeId, String currentTitle) {
    if (_isDisposing || !mounted) {
      print('⚠️ Widget正在dispose或已unmounted，跳过开始编辑操作');
      return;
    }

    // 使用Dialog进行编辑，完全避免内联编辑的unmounted问题
    _showEditDialog(nodeId, currentTitle);
  }

  /// 显示编辑Dialog
  Future<void> _showEditDialog(String nodeId, String currentTitle) async {
    if (_isDisposing || !mounted) return;

    final result = await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return _EditNodeDialog(nodeId: nodeId, initialTitle: currentTitle);
      },
    );

    if (result != null && result != currentTitle && mounted) {
      // 更新节点标题
      final node = widget.categoryManager.findNodeById(nodeId);
      if (node != null) {
        node.title = result;
        widget.onCategoriesChanged();
        print('✅ 通过Dialog更新节点标题: $nodeId -> $result');
      }
    }
  }

  // 已删除未使用的 _startEditingInline 方法

  /// 完成编辑（增强安全性检查）
  void _finishEditing(String nodeId, {bool save = true}) {
    // 检查widget是否仍然mounted且未在dispose过程中
    if (_isDisposing || !mounted) {
      print('⚠️ Widget正在dispose或已unmounted，跳过完成编辑操作');
      return;
    }

    // 防止重复调用（失去焦点和键盘完成按钮可能都会触发）
    if (_editingNodeId != nodeId) {
      print('⚠️ 当前编辑节点不匹配，跳过: 期望=$nodeId, 实际=$_editingNodeId');
      return;
    }

    if (!_editControllers.containsKey(nodeId)) {
      print('⚠️ 编辑控制器不存在，跳过: $nodeId');
      return;
    }

    // 如果是取消编辑，设置标志防止失去焦点触发保存

    try {
      if (save) {
        final controller = _editControllers[nodeId];
        if (controller != null) {
          final newTitle = controller.text.trim();
          final node = widget.categoryManager.findNodeById(nodeId);

          if (newTitle.isNotEmpty) {
            // 有效输入，保存新标题
            if (node != null) {
              node.title = newTitle;
              node.isNew = false;
              print('✅ 保存节点标题: $nodeId -> $newTitle');
              // 不在编辑过程中调用onCategoriesChanged，避免widget重建
            }
          } else {
            // 空输入的处理：区分新节点和现有节点
            if (node != null) {
              if (node.isNew) {
                // 新节点：空输入时删除节点
                print('🗑️ 删除空标题的新节点: $nodeId');
                widget.categoryManager.deleteNode(nodeId);
                // 不在编辑过程中调用onCategoriesChanged，避免widget重建
              } else {
                // 现有节点：空输入时恢复原始标题
                final originalTitle = _originalTitles[nodeId];
                if (originalTitle != null) {
                  node.title = originalTitle;
                  print('⚠️ 编辑现有节点时输入为空，已恢复原标题: $originalTitle');
                }
              }
            }
          }
        }
      } else {
        // 取消编辑，如果是新节点则删除
        final node = widget.categoryManager.findNodeById(nodeId);
        if (node != null && node.isNew) {
          print('🗑️ 取消编辑，删除新节点: $nodeId');
          widget.categoryManager.deleteNode(nodeId);
          // 不在编辑过程中调用onCategoriesChanged，避免widget重建
        }
      }

      print('✅ 完成编辑节点: $nodeId');
    } catch (e, stackTrace) {
      print('❌ 完成编辑时发生错误: $e');
      print('Stack trace: $stackTrace');
    } finally {
      // 无论如何都要清理资源
      _cleanupEditingState(nodeId);

      // 安全地更新状态
      if (!_isDisposing && mounted) {
        setState(() {
          _editingNodeId = null;
        });

        // 通知父组件编辑结束
        widget.onEditingStateChanged(false);

        // 编辑完成后只保存数据，不触发widget重建
        // 使用防抖机制避免频繁保存
        _saveTimer?.cancel();
        _saveTimer = Timer(const Duration(milliseconds: 500), () async {
          if (!_isDisposing && mounted) {
            try {
              await widget.categoryManager.saveToStorage();
              print('✅ 编辑完成后数据保存成功');
            } catch (e) {
              print('❌ 编辑完成后数据保存失败: $e');
            }
          }
        });
      }
    }
  }

  /// 安全地清理编辑状态（增强版）
  void _cleanupEditingState(String nodeId) {
    print('🧹 开始清理编辑状态: $nodeId');

    bool hasResources = false;

    // 安全地dispose focusNode
    final focusNode = _editFocusNodes[nodeId];
    if (focusNode != null) {
      hasResources = true;
      try {
        if (!focusNode.hasFocus) {
          focusNode.dispose();
          print('✅ 成功dispose FocusNode: $nodeId');
        } else {
          // 如果还有焦点，先移除焦点再dispose
          focusNode.unfocus();
          // 只有在widget仍然mounted且未在dispose过程中时才延迟dispose
          if (!_isDisposing && mounted) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              // 再次检查mounted状态
              if (!_isDisposing && mounted) {
                try {
                  focusNode.dispose();
                  print('✅ 延迟dispose FocusNode: $nodeId');
                } catch (e) {
                  print('⚠️ 延迟dispose FocusNode失败: $e');
                }
              } else {
                print('⚠️ Widget已unmounted，跳过延迟dispose FocusNode: $nodeId');
              }
            });
          } else {
            // 如果widget已经unmounted，直接尝试dispose
            try {
              focusNode.dispose();
              print('✅ 直接dispose FocusNode (widget unmounted): $nodeId');
            } catch (e) {
              print('⚠️ 直接dispose FocusNode失败: $e');
            }
          }
        }
      } catch (e) {
        // 如果已经dispose，忽略错误
        print('⚠️ FocusNode已被dispose或dispose失败: $e');
      }
    }

    // 安全地dispose controller
    final controller = _editControllers[nodeId];
    if (controller != null) {
      hasResources = true;
      try {
        controller.dispose();
        print('✅ 成功dispose TextEditingController: $nodeId');
      } catch (e) {
        // 如果已经dispose，忽略错误
        print('⚠️ TextEditingController已被dispose或dispose失败: $e');
      }
    }

    // 清理映射
    final removedController = _editControllers.remove(nodeId);
    final removedFocusNode = _editFocusNodes.remove(nodeId);
    final removedTextFieldKey = _editTextFieldKeys.remove(nodeId);
    final removedOriginalTitle = _originalTitles.remove(nodeId);

    if (hasResources ||
        removedController != null ||
        removedFocusNode != null ||
        removedTextFieldKey != null ||
        removedOriginalTitle != null) {
      print(
        '✅ 清理编辑状态完成: $nodeId (controller: ${removedController != null}, focusNode: ${removedFocusNode != null}, textFieldKey: ${removedTextFieldKey != null}, originalTitle: ${removedOriginalTitle != null})',
      );
    } else {
      print('ℹ️ 节点 $nodeId 没有需要清理的编辑状态');
    }
  }

  /// 拖拽开始
  void _onDragStarted(CategoryNode node) {
    if (_isDisposing || !mounted) return;
    setState(() {
      _draggingNodeId = node.id;
    });
    print('🔄 开始拖拽节点: ${node.title}');
  }

  /// 拖拽结束
  void _onDragEnd(DraggableDetails details) {
    if (_isDisposing || !mounted) return;
    setState(() {
      _draggingNodeId = null;
      _insertionLineTargetId = null;
      _insertionLineAbove = false;
    });
    print('🔄 拖拽结束');
  }

  /// 检查是否可以接受放置
  bool _canAcceptDrop(CategoryNode? dragData, CategoryNode targetNode) {
    if (dragData == null || _draggingNodeId == null) return false;
    if (dragData.id == targetNode.id) return false; // 不能拖拽到自己

    // 不能拖拽到自己的子节点
    if (_isDescendantOf(targetNode, dragData)) return false;

    return true;
  }

  /// 检查targetNode是否是dragNode的后代
  bool _isDescendantOf(CategoryNode targetNode, CategoryNode dragNode) {
    for (var child in dragNode.children) {
      if (child.id == targetNode.id) return true;
      if (_isDescendantOf(targetNode, child)) return true;
    }
    return false;
  }

  /// 拖拽移动（完整的上方/下方插入逻辑）
  void _onDragMove(
    DragTargetDetails<CategoryNode> details,
    CategoryNode targetNode,
  ) {
    if (_isDisposing || !mounted) return;

    // 使用精确的位置判断逻辑，支持完整的边界检测
    final targetKey = _getNodeKey(targetNode.id);
    final targetRenderBox =
        targetKey.currentContext?.findRenderObject() as RenderBox?;

    if (targetRenderBox != null) {
      // 获取目标节点的全局位置和大小
      final targetGlobalPosition = targetRenderBox.localToGlobal(Offset.zero);
      final targetSize = targetRenderBox.size;

      // 获取拖拽位置的全局Y坐标
      final dragGlobalY = details.offset.dy;

      // 计算目标节点的边界
      final targetTopY = targetGlobalPosition.dy;
      final targetBottomY = targetTopY + targetSize.height;
      final targetCenterY = targetTopY + (targetSize.height / 2);

      // 完整的边界检测逻辑
      final insertAbove = _determineInsertPosition(
        dragGlobalY,
        targetTopY,
        targetCenterY,
        targetBottomY,
        targetNode,
      );

      if (!_isDisposing && mounted) {
        setState(() {
          _insertionLineTargetId = targetNode.id;
          _insertionLineAbove = insertAbove;
        });
      }

      print('🎯 完整边界检测: ${targetNode.title}, 插入${insertAbove ? "上方" : "下方"}');
      print(
        '   拖拽Y: ${dragGlobalY.toStringAsFixed(1)}, 目标范围: [${targetTopY.toStringAsFixed(1)} - ${targetBottomY.toStringAsFixed(1)}], 中心: ${targetCenterY.toStringAsFixed(1)}',
      );
    } else {
      // 降级处理：如果无法获取RenderBox，使用原有逻辑
      _fallbackDragMoveLogic(details, targetNode);
    }
  }

  /// 确定插入位置的完整逻辑
  bool _determineInsertPosition(
    double dragY,
    double targetTop,
    double targetCenter,
    double targetBottom,
    CategoryNode targetNode,
  ) {
    // 获取目标节点在列表中的位置信息
    final isFirstNode = _isFirstNodeInList(targetNode);
    final isLastNode = _isLastNodeInList(targetNode);

    // 定义敏感区域大小（像素）- 适应紧凑间距
    const double sensitiveZone = 6.0;

    // 1. 顶部敏感区域：如果拖拽位置在目标节点顶部附近，插入上方
    if (dragY <= targetTop + sensitiveZone) {
      print('   → 顶部敏感区域检测：插入上方');
      return true;
    }

    // 2. 底部敏感区域：如果拖拽位置在目标节点底部附近，插入下方
    // 对最后一个节点扩展底部敏感区域，提高下边界检测的成功率
    final bottomSensitiveZone = isLastNode
        ? sensitiveZone * 4
        : sensitiveZone; // 最后节点扩展到24px
    if (dragY >= targetBottom - bottomSensitiveZone) {
      print(
        '   → 底部敏感区域检测：插入下方 (敏感区域: ${bottomSensitiveZone.toStringAsFixed(1)}px)',
      );
      return false;
    }

    // 3. 特殊处理：第一个节点的上方区域扩展
    if (isFirstNode && dragY < targetTop) {
      print('   → 第一个节点上方扩展区域：插入上方');
      return true;
    }

    // 4. 特殊处理：最后一个节点的下方区域扩展
    if (isLastNode && dragY > targetBottom) {
      print('   → 最后一个节点下方扩展区域：插入下方');
      return false;
    }

    // 5. 默认逻辑：基于中心点判断
    final insertAbove = dragY < targetCenter;
    print('   → 中心点判断：插入${insertAbove ? "上方" : "下方"}');
    return insertAbove;
  }

  /// 判断节点是否是其所在列表的第一个节点
  bool _isFirstNodeInList(CategoryNode targetNode) {
    // 检查是否是根节点列表的第一个
    final rootCategories = widget.categoryManager.getFilteredCategories();
    if (rootCategories.isNotEmpty && rootCategories.first.id == targetNode.id) {
      return true;
    }

    // 检查是否是某个父节点的第一个子节点
    final parent = widget.categoryManager.getParentNode(targetNode.id);
    if (parent != null &&
        parent.children.isNotEmpty &&
        parent.children.first.id == targetNode.id) {
      return true;
    }

    return false;
  }

  /// 判断节点是否是其所在列表的最后一个节点
  bool _isLastNodeInList(CategoryNode targetNode) {
    // 检查是否是根节点列表的最后一个
    final rootCategories = widget.categoryManager.getFilteredCategories();
    if (rootCategories.isNotEmpty && rootCategories.last.id == targetNode.id) {
      return true;
    }

    // 检查是否是某个父节点的最后一个子节点
    final parent = widget.categoryManager.getParentNode(targetNode.id);
    if (parent != null &&
        parent.children.isNotEmpty &&
        parent.children.last.id == targetNode.id) {
      return true;
    }

    return false;
  }

  /// 降级拖拽移动逻辑（当无法获取RenderBox时使用）
  void _fallbackDragMoveLogic(
    DragTargetDetails<CategoryNode> details,
    CategoryNode targetNode,
  ) {
    final dragY = details.offset.dy;
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;

    if (renderBox != null) {
      final widgetGlobalPosition = renderBox.localToGlobal(Offset.zero);
      final relativeY = dragY - widgetGlobalPosition.dy;

      // 使用更保守的节点高度估算
      final nodeHeight = 56.0; // 增加高度估算以适应新的卡片样式
      final nodeIndex = _getNodeDisplayIndex(targetNode);
      final nodeTopY = nodeIndex * nodeHeight;
      final nodeBottomY = nodeTopY + nodeHeight;
      final nodeCenterY = nodeTopY + (nodeHeight / 2);

      // 检查是否为最后一个节点
      final isLastNode = _isLastNodeInList(targetNode);

      // 敏感区域设置
      const sensitiveArea = 6.0;

      bool insertAbove;

      // 顶部敏感区域检测
      if (relativeY <= (nodeTopY + sensitiveArea)) {
        print('    → 顶部敏感区域检测：插入上方');
        insertAbove = true;
      }
      // 底部敏感区域检测（对最后节点扩展）
      else if (relativeY >= (nodeBottomY - sensitiveArea)) {
        print('    → 底部敏感区域检测：插入下方');
        insertAbove = false;
      }
      // 最后节点的下方扩展区域
      else if (isLastNode && relativeY > nodeBottomY) {
        print('    → 最后节点下方扩展区域：插入下方');
        insertAbove = false;
      }
      // 中心点判断
      else {
        insertAbove = relativeY < nodeCenterY;
        print('    → 中心点判断：插入${insertAbove ? "上方" : "下方"}');
      }

      if (!_isDisposing && mounted) {
        setState(() {
          _insertionLineTargetId = targetNode.id;
          _insertionLineAbove = insertAbove;
        });
      }

      print('🎯 降级位置判断: ${targetNode.title}, 插入${insertAbove ? "上方" : "下方"}');
      print(
        '    拖拽Y: ${relativeY.toStringAsFixed(1)}, 目标范围: [${nodeTopY.toStringAsFixed(1)} - ${nodeBottomY.toStringAsFixed(1)}], 中心: ${nodeCenterY.toStringAsFixed(1)}, 最后节点: $isLastNode',
      );
    } else {
      if (!_isDisposing && mounted) {
        setState(() {
          _insertionLineTargetId = targetNode.id;
          _insertionLineAbove = false;
        });
      }
    }
  }

  /// 获取节点在显示列表中的索引（用于位置计算）
  int _getNodeDisplayIndex(CategoryNode targetNode) {
    int index = 0;
    final categories = widget.categoryManager.getFilteredCategories();

    for (var category in categories) {
      if (category.id == targetNode.id) {
        return index;
      }
      index++;
      if (category.isExpanded) {
        index += _countVisibleChildren(category.children);
      }
    }
    return index;
  }

  /// 递归计算可见子节点数量
  int _countVisibleChildren(List<CategoryNode> children) {
    int count = 0;
    for (var child in children) {
      count++;
      if (child.isExpanded) {
        count += _countVisibleChildren(child.children);
      }
    }
    return count;
  }

  /// 拖拽离开
  void _onDragLeave(CategoryNode targetNode) {
    if (_isDisposing || !mounted) return;
    setState(() {
      if (_insertionLineTargetId == targetNode.id) {
        _insertionLineTargetId = null;
        _insertionLineAbove = false;
      }
    });
  }

  /// 接受放置
  void _onDropAccepted(CategoryNode dragNode, CategoryNode targetNode) {
    if (!mounted) return;

    final insertPosition = _insertionLineAbove ? "上方" : "下方";
    print(
      '🔄 拖拽放置: ${dragNode.title} -> ${targetNode.title} ($insertPosition)',
    );

    // 执行节点移动逻辑，传递插入位置信息
    _moveNodeToTarget(dragNode, targetNode, insertAbove: _insertionLineAbove);

    if (!_isDisposing && mounted) {
      setState(() {
        _insertionLineTargetId = null;
        _insertionLineAbove = false;
      });
    }
  }

  /// 构建拖拽反馈（像素级匹配原始节点高度）
  Widget _buildDragFeedback(CategoryNode node) {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2F76DA),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Container(
          // 使用与原始节点完全相同的padding结构
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(width: 12), // 左侧边距，匹配原始节点的缩进效果
              // 展开图标占位符，与原始节点保持一致
              const SizedBox(width: 20),
              const SizedBox(width: 8),
              // 节点标题，使用与原始节点相同的文本样式
              Text(
                node.title,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Colors.white, // 拖拽时使用白色文本
                ),
              ),
              const SizedBox(width: 8), // 文本与按钮之间的间距
              // PopupMenu按钮占位符，确保宽度完全匹配
              const SizedBox(width: 24), // PopupMenuButton的标准宽度
              const SizedBox(width: 12), // 右侧边距
            ],
          ),
        ),
      ),
    );
  }

  /// 构建拖拽占位符
  Widget _buildDragPlaceholder(CategoryNode node) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          const SizedBox(width: 20), // 展开图标占位
          const SizedBox(width: 8),
          Expanded(
            child: Container(
              height: 20,
              decoration: BoxDecoration(
                color: const Color(0xFFE3E2E0),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          const SizedBox(width: 40), // 更多操作按钮占位
        ],
      ),
    );
  }

  /// 移动节点到目标位置
  void _moveNodeToTarget(
    CategoryNode dragNode,
    CategoryNode targetNode, {
    bool insertAbove = false,
  }) {
    // 1. 从原位置移除拖拽节点
    _removeNodeFromOriginalPosition(dragNode);

    // 2. 将节点插入到目标位置
    _insertNodeAtTargetPosition(dragNode, targetNode, insertAbove: insertAbove);

    // 3. 更新层级
    _updateNodeLevels();

    // 4. 保存更改（延迟调用，避免立即重建widget）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isDisposing && mounted) {
        widget.onCategoriesChanged();
      }
    });

    final position = insertAbove ? "上方" : "下方";
    print('🔄 节点移动完成: ${dragNode.title} -> ${targetNode.title} ($position)');
  }

  /// 从原位置移除节点
  void _removeNodeFromOriginalPosition(CategoryNode dragNode) {
    // 从根节点列表中查找并移除
    final initialLength = widget.categoryManager.categories.length;
    widget.categoryManager.categories.removeWhere(
      (node) => node.id == dragNode.id,
    );
    if (widget.categoryManager.categories.length < initialLength) {
      return; // 已从根节点列表中移除
    }

    // 从子节点中递归查找并移除
    _removeNodeFromChildren(widget.categoryManager.categories, dragNode.id);
  }

  /// 递归从子节点中移除
  bool _removeNodeFromChildren(List<CategoryNode> nodes, String nodeId) {
    for (var node in nodes) {
      final initialLength = node.children.length;
      node.children.removeWhere((child) => child.id == nodeId);
      if (node.children.length < initialLength) {
        return true; // 已找到并移除
      }
      if (_removeNodeFromChildren(node.children, nodeId)) {
        return true;
      }
    }
    return false;
  }

  /// 将节点插入到目标位置（支持上方/下方插入）
  void _insertNodeAtTargetPosition(
    CategoryNode dragNode,
    CategoryNode targetNode, {
    bool insertAbove = false,
  }) {
    // 找到目标节点的父节点
    final targetParent = widget.categoryManager.getParentNode(targetNode.id);

    if (targetParent != null) {
      // 目标节点有父节点，根据insertAbove决定插入位置
      final targetIndex = targetParent.children.indexWhere(
        (node) => node.id == targetNode.id,
      );
      if (targetIndex != -1) {
        final insertIndex = insertAbove ? targetIndex : targetIndex + 1;
        targetParent.children.insert(insertIndex, dragNode);
        dragNode.level = targetNode.level; // 设置为相同层级

        print('📍 插入到父节点子列表: index=$insertIndex, ${insertAbove ? "上方" : "下方"}');
      }
    } else {
      // 目标节点是根节点，根据insertAbove决定插入位置
      final targetIndex = widget.categoryManager.categories.indexWhere(
        (node) => node.id == targetNode.id,
      );
      if (targetIndex != -1) {
        final insertIndex = insertAbove ? targetIndex : targetIndex + 1;
        widget.categoryManager.categories.insert(insertIndex, dragNode);
        dragNode.level = 0; // 根节点层级为0

        print('📍 插入到根节点列表: index=$insertIndex, ${insertAbove ? "上方" : "下方"}');
      }
    }
  }

  /// 更新所有节点的层级
  void _updateNodeLevels() {
    _updateNodeLevelsRecursive(widget.categoryManager.categories, 0);
  }

  /// 递归更新节点层级
  void _updateNodeLevelsRecursive(List<CategoryNode> nodes, int level) {
    for (var node in nodes) {
      node.level = level;
      _updateNodeLevelsRecursive(node.children, level + 1);
    }
  }

  List<Widget> _buildTreeNodes(List<CategoryNode> nodes) {
    List<Widget> widgets = [];
    for (var node in nodes) {
      final isEditing = _editingNodeId == node.id;

      widgets.add(
        Material(
          color: Colors.transparent,
          child: Container(
            padding: EdgeInsets.only(
              left: 16.0 + node.level * 24.0, // 根据层级缩进
              top: 2, // 减少垂直间距，提供更紧凑的布局
              bottom: 2,
              right: 8,
            ),
            child: isEditing ? _buildEditingNode(node) : _buildNormalNode(node),
          ),
        ),
      );

      if (node.isExpanded && !isEditing) {
        widgets.addAll(_buildTreeNodes(node.children));
      }
    }
    return widgets;
  }

  /// 构建普通节点（支持拖拽）
  Widget _buildNormalNode(CategoryNode node) {
    return _buildDraggableNode(node);
  }

  /// 构建可拖拽的节点
  Widget _buildDraggableNode(CategoryNode node) {
    return LongPressDraggable<CategoryNode>(
      data: node,
      feedback: _buildDragFeedback(node),
      childWhenDragging: _buildDragPlaceholder(node),
      onDragStarted: () => _onDragStarted(node),
      onDragEnd: (details) => _onDragEnd(details),
      child: DragTarget<CategoryNode>(
        onWillAcceptWithDetails: (details) =>
            _canAcceptDrop(details.data, node),
        onAcceptWithDetails: (details) => _onDropAccepted(details.data, node),
        onMove: (details) => _onDragMove(details, node),
        onLeave: (data) => _onDragLeave(node),
        builder: (context, candidateData, rejectedData) {
          return _buildNodeWithInsertionLine(node);
        },
      ),
    );
  }

  /// 构建带分界线的节点（新的视觉反馈机制）
  Widget _buildNodeWithInsertionLine(CategoryNode node) {
    final showInsertionLineAbove =
        _insertionLineTargetId == node.id && _insertionLineAbove;
    final showInsertionLineBelow =
        _insertionLineTargetId == node.id && !_insertionLineAbove;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 上方分界线
        if (showInsertionLineAbove) _buildInsertionLine(),
        // 节点内容
        _buildNodeContent(node),
        // 下方分界线
        if (showInsertionLineBelow) _buildInsertionLine(),
      ],
    );
  }

  /// 构建节点内容（移除目标位置文本框显示）
  Widget _buildNodeContent(CategoryNode node) {
    return Container(
      key: _getNodeKey(node.id), // 添加GlobalKey以支持精确位置检测
      child: InkWell(
        onTap: () {
          if (node.children.isNotEmpty) {
            if (!_isDisposing && mounted) {
              setState(() {
                node.isExpanded = !node.isExpanded;
              });
            }
          } else {
            widget.onNodeSelected(node);
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              // 展开/收起图标
              if (node.children.isNotEmpty)
                Icon(
                  node.isExpanded ? Icons.arrow_drop_down : Icons.arrow_right,
                  size: 20,
                  color: const Color(0xFF787774),
                )
              else
                const SizedBox(width: 20), // 占位符保持对齐
              const SizedBox(width: 8),
              // 节点标题
              Expanded(
                child: Text(
                  node.title,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: node.isNew
                        ? const Color(0xFF2F76DA)
                        : const Color(0xFF37352F),
                  ),
                ),
              ),
              // 隐私状态和更多操作
              // 在编辑过程中禁用PopupMenuButton，防止unmounted错误
              _editingNodeId != null
                  ? const SizedBox(width: 24, height: 24)
                  : // 占位符保持布局
                    PopupMenuButton<String>(
                      key: _getPopupMenuKey(node.id),
                      onSelected: (action) {
                        // 立即检查widget状态，避免在dispose后执行
                        if (_isDisposing || !mounted) {
                          print(
                            '⚠️ Widget正在dispose或已unmounted，跳过PopupMenu操作: $action',
                          );
                          return;
                        }

                        // 使用延迟执行，确保PopupMenu完全关闭后再执行操作
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          // 双重检查：确保widget仍然有效
                          if (!_isDisposing && mounted) {
                            _handleNodeAction(node, action);
                          } else {
                            print('⚠️ Widget状态已变更，跳过延迟执行的PopupMenu操作: $action');
                          }
                        });
                      },
                      // Notion风格的弹窗样式
                      color: Colors.white,
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(
                          color: const Color(0xFFE3E2E0),
                          width: 1,
                        ),
                      ),
                      itemBuilder: (popupContext) => [
                        _buildCategoryPopupMenuItem(
                          value: 'edit',
                          icon: Icons.edit_outlined,
                          text: '编辑',
                          color: const Color(0xFF2F76DA),
                        ),
                        _buildCategoryPopupMenuItem(
                          value: 'add_child',
                          icon: Icons.create_new_folder_outlined,
                          text: '添加子分类',
                          color: const Color(0xFF2F76DA),
                        ),
                        const PopupMenuDivider(height: 1),
                        _buildCategoryPopupMenuItem(
                          value: 'public',
                          icon: Icons.public_outlined,
                          text: '设为公共',
                          color: const Color(0xFF37352F),
                        ),
                        _buildCategoryPopupMenuItem(
                          value: 'private',
                          icon: Icons.lock_outline,
                          text: '设为隐私',
                          color: const Color(0xFF37352F),
                        ),
                        const PopupMenuDivider(height: 1),
                        _buildCategoryPopupMenuItem(
                          value: 'delete',
                          icon: Icons.delete_outline,
                          text: '删除',
                          color: Colors.red.shade400,
                        ),
                      ],
                      icon: const Icon(
                        Icons.more_horiz,
                        color: Color(0xFF37352F),
                        size: 20,
                      ),
                      tooltip: '更多操作',
                      padding: EdgeInsets.zero,
                      splashRadius: 20,
                      // 添加错误处理和取消处理
                      onCanceled: () {
                        // PopupMenu被取消时确保没有遗留状态
                        if (mounted) {
                          print('🔄 PopupMenu已取消');
                        }
                      },
                    ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建插入位置分界线
  Widget _buildInsertionLine() {
    return Container(
      height: 3.0,
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 2.0),
      decoration: BoxDecoration(
        color: const Color(0xFF2F76DA),
        borderRadius: BorderRadius.circular(1.5),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2F76DA).withValues(alpha: 0.3),
            blurRadius: 4.0,
            offset: const Offset(0, 1),
          ),
        ],
      ),
    );
  }

  /// 构建编辑中的节点（简化版，移除层级调整按钮）
  Widget _buildEditingNode(CategoryNode node) {
    final controller = _editControllers[node.id]!;
    final focusNode = _editFocusNodes[node.id]!;

    return Row(
      children: [
        // 展开/收起图标（编辑时显示为占位符）
        const SizedBox(width: 20),
        const SizedBox(width: 8),
        // 拖拽手柄占位符（编辑时不可拖拽）
        const Icon(Icons.edit, size: 16, color: Color(0xFF2F76DA)),
        const SizedBox(width: 8),
        // 编辑输入框 - 使用StatefulBuilder来隔离状态
        Expanded(
          child: StatefulBuilder(
            builder: (context, setTextFieldState) {
              return TextField(
                key: _editTextFieldKeys[node.id],
                controller: controller,
                focusNode: focusNode,
                style: const TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(
                      color: Color(0xFF2F76DA),
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  isDense: true,
                ),
                onSubmitted: (_) {
                  // 检查widget状态和编辑状态
                  if (!_isDisposing &&
                      mounted &&
                      _editingNodeId == node.id &&
                      _editControllers.containsKey(node.id)) {
                    _finishEditing(node.id, save: true);
                  }
                },
                onEditingComplete: () {
                  // 检查widget状态和编辑状态
                  if (!_isDisposing &&
                      mounted &&
                      _editingNodeId == node.id &&
                      _editControllers.containsKey(node.id)) {
                    _finishEditing(node.id, save: true);
                  }
                },
              );
            },
          ),
        ),
        const SizedBox(width: 8),
        // 取消编辑按钮
        IconButton(
          icon: const Icon(Icons.close, size: 18, color: Color(0xFF9B9A97)),
          onPressed: () {
            // 检查widget状态和编辑状态
            if (!_isDisposing &&
                mounted &&
                _editingNodeId == node.id &&
                _editControllers.containsKey(node.id)) {
              _finishEditing(node.id, save: false);
            }
          },
          tooltip: '取消编辑',
          padding: const EdgeInsets.all(2),
          constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
        ),
      ],
    );
  }

  /// 处理节点操作（增强安全性检查）
  void _handleNodeAction(CategoryNode node, String action) {
    // 确保widget仍然mounted且未在dispose过程中
    if (_isDisposing || !mounted) {
      print('⚠️ Widget正在dispose或已unmounted，跳过节点操作: $action');
      return;
    }

    print('🎯 处理节点操作: ${node.title} -> $action');

    try {
      switch (action) {
        case 'edit':
          if (mounted) {
            _startEditing(node.id, node.title);
          }
          break;
        case 'add_child':
          if (mounted) {
            _addChildCategory(node);
          }
          break;
        case 'delete':
          if (mounted) {
            _showDeleteConfirmation(node);
          }
          break;
        case 'public':
          if (mounted) {
            // 延迟执行setState，避免在PopupMenu关闭过程中重建widget
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (!_isDisposing && mounted) {
                setState(() {
                  node.privacyStatus = PrivacyStatus.public;
                });
                print('✅ 设置节点为公共: ${node.title}');
                widget.onCategoriesChanged();
              }
            });
          }
          break;
        case 'private':
          if (mounted) {
            // 延迟执行setState，避免在PopupMenu关闭过程中重建widget
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (!_isDisposing && mounted) {
                setState(() {
                  node.privacyStatus = PrivacyStatus.private;
                });
                print('✅ 设置节点为隐私: ${node.title}');
                widget.onCategoriesChanged();
              }
            });
          }
          break;
        default:
          print('⚠️ 未知的节点操作: $action');
      }
    } catch (e, stackTrace) {
      print('❌ 处理节点操作时发生错误: $e');
      print('Stack trace: $stackTrace');
    }
  }

  /// 显示删除确认对话框（添加mounted检查）
  void _showDeleteConfirmation(CategoryNode node) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('确认删除'),
        content: Text(
          '确定要删除分类"${node.title}"吗？\n${node.children.isNotEmpty ? '注意：这将同时删除所有子分类。' : ''}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              // 直接执行删除操作，移除Future.microtask避免unmounted widget错误
              if (mounted) {
                widget.categoryManager.deleteNode(node.id);
                print('✅ 删除节点: ${node.title}');
                // 延迟调用onCategoriesChanged，避免立即重建widget
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (!_isDisposing && mounted) {
                    widget.onCategoriesChanged();
                  }
                });
              }
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// 构建符合Notion风格的分类弹窗菜单项
  PopupMenuItem<String> _buildCategoryPopupMenuItem({
    required String value,
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return PopupMenuItem<String>(
      value: value,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: color, size: 18),
          const SizedBox(width: 12),
          Text(
            text,
            style: TextStyle(
              color: value == 'delete' ? color : const Color(0xFF37352F),
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final sidebarWidth = screenWidth > 600
        ? 320.0
        : screenWidth * 0.7; // 调整宽度为70%

    return Visibility(
      visible: widget.isVisible,
      child: Stack(
        children: [
          // 背景遮罩层
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                // 如果有正在编辑的节点，先保存再关闭
                if (_editingNodeId != null) {
                  _finishEditing(_editingNodeId!, save: true);
                }
                widget.onClose();
              },
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
                child: Container(color: Colors.black.withValues(alpha: 0.2)),
              ),
            ),
          ),
          // 侧边栏
          AnimatedPositioned(
            duration: const Duration(milliseconds: 250),
            curve: Curves.easeInOut,
            left: widget.isVisible ? 0 : -sidebarWidth,
            top: 0,
            bottom: 0,
            width: sidebarWidth,
            child: Material(
              elevation: 8,
              child: GestureDetector(
                onTap: () {
                  // 点击侧边栏空白区域时，如果有正在编辑的节点，则保存并结束编辑
                  if (!_isDisposing && mounted && _editingNodeId != null) {
                    final nodeId = _editingNodeId!;
                    FocusScope.of(context).unfocus();
                    _finishEditing(nodeId, save: true);
                  }
                },
                child: Container(
                  color: Colors.white,
                  child: SafeArea(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 顶部操作栏
                        _buildTopBar(),
                        // 分类树列表
                        Expanded(
                          child: ListView(
                            padding: EdgeInsets.zero,
                            children: _buildTreeNodes(
                              widget.categoryManager.getFilteredCategories(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 记忆宫殿卡片组件
class MemoryPalaceCard extends StatelessWidget {
  final MemoryPalace palace;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final VoidCallback? onMoreOptions; // 新增三点菜单回调

  const MemoryPalaceCard({
    super.key,
    required this.palace,
    required this.onTap,
    required this.onLongPress,
    this.onMoreOptions, // 可选的三点菜单回调
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0.5,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Stack(
        children: [
          // 主要内容区域
          InkWell(
            onTap: onTap,
            onLongPress: onLongPress,
            borderRadius: BorderRadius.circular(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      image: palace.imagePath.isNotEmpty
                          ? DecorationImage(
                              image:
                                  palace.imagePath.startsWith('http://') ||
                                      palace.imagePath.startsWith('https://')
                                  ? NetworkImage(palace.imagePath)
                                  : FileImage(File(palace.imagePath))
                                        as ImageProvider,
                              fit: BoxFit.cover,
                            )
                          : null,
                      color: palace.imagePath.isEmpty ? Colors.grey[300] : null,
                    ),
                    child: palace.imagePath.isEmpty
                        ? const Center(
                            child: Icon(
                              Icons.image,
                              size: 48,
                              color: Colors.grey,
                            ),
                          )
                        : null,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        palace.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${palace.anchorCount}个知识点',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 三点菜单按钮（右下角）
          if (onMoreOptions != null)
            Positioned(
              bottom: 8,
              right: 8,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onMoreOptions,
                  borderRadius: BorderRadius.circular(16),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: const Color(0xFFE3E2E0),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.more_horiz,
                      size: 18,
                      color: Color(0xFF37352F),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// 编辑知忆相册对话框
class _EditAlbumDialog extends StatefulWidget {
  final MemoryPalace palace;
  final List<CategoryNode> categoryData;
  final Function(PhotoAlbum) onAlbumUpdated;

  const _EditAlbumDialog({
    required this.palace,
    required this.categoryData,
    required this.onAlbumUpdated,
  });

  @override
  State<_EditAlbumDialog> createState() => _EditAlbumDialogState();
}

class _EditAlbumDialogState extends State<_EditAlbumDialog>
    with TickerProviderStateMixin {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  List<String> _selectedImagePaths = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _selectedCategory;

  // 动画控制器
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    // 初始化表单数据
    _titleController.text = widget.palace.title;
    _selectedCategory = widget.palace.category;
    _selectedImagePaths = List.from(widget.palace.imagePaths);

    // 初始化动画
    _initializeAnimations();

    // 如果有图片，启动动画
    if (_selectedImagePaths.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fadeAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _slideAnimationController,
            curve: Curves.easeOutCubic,
          ),
        );
  }

  /// 直接选择图片（移除拍照选项）
  Future<void> _showImagePickerOptions() async {
    // 直接调用图片选择，不再显示选项菜单
    await _pickImages();
  }

  /// 选择图片
  Future<void> _pickImages() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 先尝试单张选择，如果成功再尝试多张选择
      List<XFile> images = [];

      try {
        // 首先尝试多张选择
        images = await _imagePicker.pickMultiImage(
          imageQuality: 80, // 压缩质量，优化性能
        );
      } catch (e) {
        print('❌ 多张选择失败，尝试单张选择: $e');
        // 如果多张选择失败，尝试单张选择
        final XFile? singleImage = await _imagePicker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 80,
        );
        if (singleImage != null) {
          images = [singleImage];
        }
      }

      if (images.isNotEmpty) {
        // 🔧 新增：图片压缩处理
        final originalPaths = images.map((image) => image.path).toList();
        print('📸 选择了 ${originalPaths.length} 张图片，开始压缩处理...');

        // 批量压缩图片
        final compressedPaths = await ImageCompressionUtils.compressImages(
          imagePaths: originalPaths,
          onProgress: (current, total) {
            print('🔧 压缩进度: $current/$total');
          },
        );

        setState(() {
          _selectedImagePaths.addAll(compressedPaths);
        });

        // 如果是第一次添加图片，启动动画
        if (_selectedImagePaths.length == compressedPaths.length) {
          _fadeAnimationController.forward();
          _slideAnimationController.forward();
        }

        // 触觉反馈
        HapticFeedback.lightImpact();

        // 显示压缩完成提示
        if (compressedPaths.length == originalPaths.length) {
          _showSnackBar('已成功处理 ${compressedPaths.length} 张图片', isError: false);
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = '选择图片失败：${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 清空所有图片
  Future<void> _clearAllImages() async {
    final bool? confirmed = await _showConfirmDialog(
      title: '清空全部图片',
      content: '确定要清空所有已选择的图片吗？此操作无法撤销。',
      confirmText: '清空',
      cancelText: '取消',
    );

    if (confirmed == true) {
      setState(() {
        _selectedImagePaths.clear();
      });

      // 重置动画
      _fadeAnimationController.reset();
      _slideAnimationController.reset();

      // 触觉反馈
      HapticFeedback.mediumImpact();

      _showSnackBar('已清空所有图片', isError: false);
    }
  }

  /// 移除单张图片
  void _removeImage(int index) {
    setState(() {
      _selectedImagePaths.removeAt(index);
    });

    // 如果没有图片了，重置动画
    if (_selectedImagePaths.isEmpty) {
      _fadeAnimationController.reset();
      _slideAnimationController.reset();
    }

    HapticFeedback.lightImpact();
  }

  /// 重新排序图片
  void _reorderImages(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final String item = _selectedImagePaths.removeAt(oldIndex);
      _selectedImagePaths.insert(newIndex, item);
    });

    HapticFeedback.selectionClick();
  }

  /// 显示确认对话框
  Future<bool?> _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required String cancelText,
  }) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFEB5757),
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 更新相册
  Future<void> _updateAlbum() async {
    final title = _titleController.text.trim();
    if (title.isEmpty) {
      _showSnackBar('请输入相册标题');
      return;
    }

    if (_selectedImagePaths.isEmpty) {
      _showSnackBar('请至少保留一张图片');
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final updatedAlbum = PhotoAlbum(
        id: widget.palace.id,
        title: title,
        imagePaths: List.from(_selectedImagePaths), // 使用编辑后的图片列表
        createdAt: widget.palace.createdAt,
        lastModified: DateTime.now(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        category: _selectedCategory,
      );

      // 调用回调函数
      widget.onAlbumUpdated(updatedAlbum);

      // 显示简短的成功消息
      _showSnackBar(
        '成功更新相册"$title"',
        isError: false,
        duration: const Duration(milliseconds: 1500),
      );

      // 短暂延迟让用户看到成功消息，然后关闭对话框
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        Navigator.of(context).pop();
      }
    } catch (e) {
      _showSnackBar('更新相册失败：${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 显示提示消息
  void _showSnackBar(
    String message, {
    bool isError = true,
    Duration? duration,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? const Color(0xFFEB5757)
            : const Color(0xFF4CAF50),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: duration ?? const Duration(seconds: 4), // 默认4秒，可自定义
      ),
    );
  }

  /// 构建图片选择按钮
  Widget _buildImagePickerButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : _showImagePickerOptions,
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.add_photo_alternate_outlined),
        label: Text(
          _selectedImagePaths.isEmpty ? '从相册选择图片' : '添加更多图片',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2F76DA),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          disabledBackgroundColor: const Color(
            0xFF2F76DA,
          ).withValues(alpha: 0.6),
        ),
      ),
    );
  }

  /// 构建图片预览列表（支持拖拽排序和响应式设计）
  Widget _buildImagePreviewList() {
    if (_selectedImagePaths.isEmpty) {
      return const SizedBox.shrink();
    }

    // 响应式设计：根据屏幕宽度调整图片大小
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final imageSize = isTablet ? 120.0 : 90.0; // 对话框中使用较小尺寸
    final spacing = isTablet ? 12.0 : 8.0;

    return SizedBox(
      height: imageSize + 20, // 额外空间用于拖拽效果
      child: ReorderableListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImagePaths.length,
        onReorder: _reorderImages,
        proxyDecorator: (child, index, animation) {
          return AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              final double animValue = Curves.easeInOut.transform(
                animation.value,
              );
              final double elevation = lerpDouble(0, 6, animValue)!;
              final double scale = lerpDouble(1, 1.05, animValue)!;

              return Transform.scale(
                scale: scale,
                child: Material(
                  elevation: elevation,
                  borderRadius: BorderRadius.circular(12),
                  child: child,
                ),
              );
            },
            child: child,
          );
        },
        itemBuilder: (context, index) {
          final imagePath = _selectedImagePaths[index];
          return Container(
            key: ValueKey(imagePath),
            width: imageSize,
            height: imageSize,
            margin: EdgeInsets.only(
              right: index < _selectedImagePaths.length - 1 ? spacing : 0,
            ),
            child: Stack(
              children: [
                // 图片容器
                Container(
                  width: imageSize,
                  height: imageSize,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0xFFE3E2E0),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(11),
                    child: ImageRenderingFix.buildOptimizedImageFile(
                      File(imagePath),
                      fit: BoxFit.cover,
                      errorWidget: Container(
                        color: const Color(0xFFF5F5F5),
                        child: const Icon(
                          Icons.broken_image,
                          color: Color(0xFF9E9E9E),
                          size: 32,
                        ),
                      ),
                    ),
                  ),
                ),

                // 封面标识
                if (index == 0)
                  Positioned(
                    bottom: 4,
                    left: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2F76DA),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        '封面',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                // 删除按钮
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: const Color(0xFFEB5757),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),

                // 拖拽指示器
                Positioned(
                  bottom: 4,
                  right: 4,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Icon(
                      Icons.drag_indicator,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 获取安全的对话框布局参数，确保避开刘海屏和动态岛
    final safeInsets = SafeAreaHelper.getDialogSafeInsets(context);
    final screenSize = MediaQuery.of(context).size;

    // 计算弹窗的最大高度，确保在屏幕上方显示
    final maxDialogHeight =
        screenSize.height - safeInsets.top - safeInsets.bottom - 40;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.only(
        top: safeInsets.top,
        bottom: safeInsets.bottom,
        left: safeInsets.left,
        right: safeInsets.right,
      ),
      alignment: Alignment.topCenter, // 关键修改：将弹窗对齐到屏幕上方
      child: Container(
        width: screenSize.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: maxDialogHeight.clamp(400, screenSize.height * 0.8),
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFE3E2E0), width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  const Expanded(
                    child: Text(
                      '编辑相册',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF37352F),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF6E6E6E)),
                    style: IconButton.styleFrom(
                      backgroundColor: const Color(0xFFF5F5F5),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 1. 图片选择按钮（移到最顶部）
                    _buildImagePickerButton(),

                    const SizedBox(height: 16),

                    // 错误消息显示
                    if (_errorMessage != null) ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEB5757).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: const Color(
                              0xFFEB5757,
                            ).withValues(alpha: 0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: const Color(0xFFEB5757),
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: const TextStyle(
                                  color: Color(0xFFEB5757),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // 2. 图片预览区域（带动画效果）
                    if (_selectedImagePaths.isNotEmpty) ...[
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: SlideTransition(
                          position: _slideAnimation,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F9FA),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: const Color(
                                  0xFF37352F,
                                ).withValues(alpha: 0.08),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '已选择 ${_selectedImagePaths.length} 张图片',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xFF37352F),
                                      ),
                                    ),
                                    TextButton.icon(
                                      onPressed: _clearAllImages,
                                      icon: const Icon(
                                        Icons.clear_all,
                                        size: 18,
                                        color: Color(0xFFEB5757),
                                      ),
                                      label: const Text(
                                        '清空全部',
                                        style: TextStyle(
                                          color: Color(0xFFEB5757),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      style: TextButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: const Color(
                                      0xFF2F76DA,
                                    ).withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        color: Color(0xFF2F76DA),
                                        size: 16,
                                      ),
                                      SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          '第一张图片将作为相册封面',
                                          style: TextStyle(
                                            color: Color(0xFF2F76DA),
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // 图片预览列表
                                _buildImagePreviewList(),

                                const SizedBox(height: 16),

                                // 从相册添加图片按钮
                                SizedBox(
                                  width: double.infinity,
                                  child: OutlinedButton.icon(
                                    onPressed: _showImagePickerOptions,
                                    icon: const Icon(
                                      Icons.photo_library_outlined,
                                    ),
                                    label: const Text('从相册添加图片'),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: const Color(0xFF2F76DA),
                                      side: const BorderSide(
                                        color: Color(0xFF2F76DA),
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 12,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],

                    // 3. 相册信息输入区域（移到图片预览区域下方）
                    _buildAlbumInfoSection(),
                  ],
                ),
              ),
            ),
            // 底部按钮
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFF6E6E6E),
                        side: const BorderSide(color: Color(0xFFE3E2E0)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('取消'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _updateAlbum,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2F76DA),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : const Text(
                              '保存更改',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建相册信息输入区域
  Widget _buildAlbumInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '相册信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),

          // 相册标题输入
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: '相册标题',
              hintText: '请输入相册标题',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFFE3E2E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFF2F76DA), width: 2),
              ),
              labelStyle: TextStyle(color: Color(0xFF6E6E6E)),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
            maxLength: 50,
            textInputAction: TextInputAction.next,
          ),

          const SizedBox(height: 16),

          // 相册描述输入
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: '相册描述（可选）',
              hintText: '请输入相册描述',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFFE3E2E0)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                borderSide: BorderSide(color: Color(0xFF2F76DA), width: 2),
              ),
              labelStyle: TextStyle(color: Color(0xFF6E6E6E)),
            ),
            style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
            maxLines: 3,
            maxLength: 200,
            textInputAction: TextInputAction.done,
          ),

          const SizedBox(height: 16),

          // 分类选择区域
          _buildCategorySection(),
        ],
      ),
    );
  }

  /// 构建分类选择区域
  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '分类',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE3E2E0)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            _selectedCategory ?? '未选择分类',
            style: TextStyle(
              fontSize: 16,
              color: _selectedCategory != null
                  ? const Color(0xFF37352F)
                  : const Color(0xFF6E6E6E),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '当前分类：${_selectedCategory ?? '无'}',
          style: const TextStyle(fontSize: 12, color: Color(0xFF6E6E6E)),
        ),
      ],
    );
  }
}

/// 分类选择对话框
class _CategorySelectionDialog extends StatefulWidget {
  final List<CategoryNode> categoryData;
  final String currentCategory;
  final String palaceTitle;

  const _CategorySelectionDialog({
    required this.categoryData,
    required this.currentCategory,
    required this.palaceTitle,
  });

  @override
  State<_CategorySelectionDialog> createState() =>
      _CategorySelectionDialogState();
}

class _CategorySelectionDialogState extends State<_CategorySelectionDialog> {
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.currentCategory;
  }

  /// 递归构建分类树
  List<Widget> _buildCategoryTree(List<CategoryNode> nodes, {int level = 0}) {
    List<Widget> widgets = [];

    for (final node in nodes) {
      widgets.add(
        InkWell(
          onTap: () {
            setState(() {
              _selectedCategory = node.title;
            });
          },
          child: Container(
            padding: EdgeInsets.only(
              left: 16.0 + (level * 20.0),
              right: 16.0,
              top: 12.0,
              bottom: 12.0,
            ),
            decoration: BoxDecoration(
              color: _selectedCategory == node.title
                  ? const Color(0xFF2F76DA).withValues(alpha: 0.1)
                  : null,
              border: Border(
                bottom: BorderSide(color: const Color(0xFFE3E2E0), width: 0.5),
              ),
            ),
            child: Row(
              children: [
                if (level > 0)
                  Icon(
                    Icons.subdirectory_arrow_right,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                if (level > 0) const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    node.title,
                    style: TextStyle(
                      fontSize: 16,
                      color: _selectedCategory == node.title
                          ? const Color(0xFF2F76DA)
                          : const Color(0xFF37352F),
                      fontWeight: _selectedCategory == node.title
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
                if (_selectedCategory == node.title)
                  const Icon(Icons.check, color: Color(0xFF2F76DA), size: 20),
              ],
            ),
          ),
        ),
      );

      // 递归添加子节点
      if (node.children.isNotEmpty) {
        widgets.addAll(_buildCategoryTree(node.children, level: level + 1));
      }
    }

    return widgets;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFE3E2E0), width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '更改分类',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '为"${widget.palaceTitle}"选择新分类',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF6E6E6E),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF6E6E6E)),
                    style: IconButton.styleFrom(
                      backgroundColor: const Color(0xFFF5F5F5),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 当前分类显示
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFFAFAFA),
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '当前分类',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF6E6E6E),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.currentCategory,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                ],
              ),
            ),
            // 分类列表
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: _buildCategoryTree(widget.categoryData),
                ),
              ),
            ),
            // 底部按钮
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFF6E6E6E),
                        side: const BorderSide(color: Color(0xFFE3E2E0)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('取消'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed:
                          _selectedCategory != null &&
                              _selectedCategory != widget.currentCategory
                          ? () => Navigator.of(context).pop(_selectedCategory)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2F76DA),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        '确认更改',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 编辑节点Dialog - 独立的编辑界面，避免内联编辑的unmounted问题
class _EditNodeDialog extends StatefulWidget {
  final String nodeId;
  final String initialTitle;

  const _EditNodeDialog({required this.nodeId, required this.initialTitle});

  @override
  State<_EditNodeDialog> createState() => _EditNodeDialogState();
}

class _EditNodeDialogState extends State<_EditNodeDialog> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialTitle);
    _focusNode = FocusNode();

    // 延迟聚焦
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _focusNode.requestFocus();
        _controller.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _controller.text.length,
        );
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _save() {
    final newTitle = _controller.text.trim();
    if (newTitle.isNotEmpty && mounted) {
      Navigator.of(context).pop(newTitle);
    }
  }

  void _cancel() {
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        '编辑分类',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Color(0xFF37352F),
        ),
      ),
      content: TextField(
        controller: _controller,
        focusNode: _focusNode,
        decoration: const InputDecoration(
          hintText: '请输入分类名称',
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        onSubmitted: (_) => _save(),
      ),
      actions: [
        TextButton(
          onPressed: _cancel,
          child: const Text('取消', style: TextStyle(color: Color(0xFF6E6E6E))),
        ),
        ElevatedButton(
          onPressed: _save,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2F76DA),
            foregroundColor: Colors.white,
          ),
          child: const Text('保存'),
        ),
      ],
    );
  }
}
