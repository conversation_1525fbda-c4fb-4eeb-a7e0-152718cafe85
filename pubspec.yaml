name: oneday
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # 状态管理
  flutter_riverpod: ^2.5.1

  # 路由管理
  go_router: ^14.2.7

  # 数学向量计算
  vector_math: ^2.1.4

  # 图片选择器
  image_picker: ^1.0.7

  # 图片裁剪器
  image_cropper: ^8.0.2

  # 权限管理
  permission_handler: ^11.3.0

  # 设备信息
  device_info_plus: ^10.1.0

  # 本地存储
  shared_preferences: ^2.2.2

  # 日期格式化
  intl: ^0.20.2
  
  # 文件系统路径
  path_provider: ^2.1.5
  path: ^1.8.3
  
  # 分享功能
  share_plus: ^7.2.2

  # 图片处理和合成
  image: ^4.1.7

  # 应用信息
  package_info_plus: ^4.2.0
  smooth_page_indicator: ^1.2.1

  # JSON序列化
  json_annotation: ^4.8.1

  # 图表库
  fl_chart: ^0.68.0
  url_launcher: ^6.3.2
  pdf: ^3.11.3
  screenshot: ^3.0.0
  fluwx: ^5.7.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # 测试相关依赖
  mockito: ^5.4.4

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  
  # 应用图标生成器
  flutter_launcher_icons: ^0.13.1

  # 代码生成
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
  state_notifier: any
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Enable internationalization
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/icons/android/
    - assets/icons/ios/
    - assets/data/vocabulary.json
    - assets/data/word_roots.json
    - assets/data/prefixes.json
    - assets/data/suffixes.json
    - assets/data/profanity_words.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # 自定义字体配置
  fonts:
    - family: NotoSansSC
      fonts:
        - asset: assets/fonts/NotoSansSC-Regular.ttf
          weight: 400

# 应用图标配置
flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"
  remove_alpha_ios: true
